#!/usr/bin/env python3
"""
癫痫信号与伪装重建信号对比可视化
Epilepsy Signal vs Disguised Reconstructed Signal Comparison Visualization

功能:
1. 加载训练好的GAN生成器
2. 重建原始EEG信号段
3. 对比癫痫信号vs伪装信号
4. 展示病灶掩盖效果
5. 分析特征差异
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import warnings
import os
import gzip
import json
import joblib
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class SignalComparisonVisualizer:
    """
    信号对比可视化器
    """
    
    def __init__(self):
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        # 数据
        self.original_signals = None
        self.original_features = None
        self.original_labels = None
        self.high_conf_epilepsy_features = None
        self.high_conf_control_features = None
        
        print("信号对比可视化器初始化")
    
    def load_models_and_data(self):
        """
        加载模型和数据
        """
        print("加载模型和数据...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        # 加载特征数据
        self.original_features = np.load('wavelet_classifier_results/extracted_features.npy')
        self.original_labels = np.load('wavelet_classifier_results/labels.npy')
        self.high_conf_epilepsy_features = np.load('confidence_gan_results/high_conf_epilepsy_features.npy')
        self.high_conf_control_features = np.load('confidence_gan_results/high_conf_control_features.npy')
        
        print(f"数据加载完成:")
        print(f"- 原始特征: {self.original_features.shape}")
        print(f"- 高置信度癫痫特征: {self.high_conf_epilepsy_features.shape}")
        print(f"- 高置信度控制组特征: {self.high_conf_control_features.shape}")
        
        return True
    
    def reconstruct_original_signals(self):
        """
        重建原始EEG信号段 (从特征反推到信号)
        """
        print("重建原始EEG信号段...")
        
        # 由于我们只有特征，我们需要重新加载原始信号
        # 这里我们使用一个简化的方法：直接从FC5通道重新提取信号段
        
        # 加载Nigeria数据集元数据
        nigeria_metadata_path = "1252141/metadata_nigeria.csv"
        metadata = pd.read_csv(nigeria_metadata_path)
        
        # 分离控制组和癫痫组文件
        control_files = metadata[metadata['Group'] == 'control']['csv.file'].tolist()
        epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        
        # 重新提取一些信号段用于可视化
        control_signals = self.extract_signal_segments(control_files[:5], 'FC5', "1252141/EEGs_Nigeria")
        epilepsy_signals = self.extract_signal_segments(epilepsy_files[:5], 'FC5', "1252141/EEGs_Nigeria")
        
        print(f"重建信号完成:")
        print(f"- 控制组信号段: {len(control_signals)}")
        print(f"- 癫痫组信号段: {len(epilepsy_signals)}")
        
        return control_signals, epilepsy_signals
    
    def extract_signal_segments(self, file_list, channel, data_dir):
        """
        提取信号段
        """
        all_segments = []
        segment_length = 512
        
        for filename in file_list:
            file_path = os.path.join(data_dir, filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                    
                    if channel in df.columns:
                        channel_data = df[channel].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values
                        
                        # 预处理
                        channel_data = self.preprocess_signal(channel_data)
                        
                        # 创建段
                        step_size = segment_length // 4
                        for i in range(0, len(channel_data) - segment_length + 1, step_size):
                            segment = channel_data[i:i + segment_length]
                            if np.std(segment) > 0.01:
                                all_segments.append(segment)
                                
                                if len(all_segments) >= 20:  # 限制数量
                                    break
                        
                        if len(all_segments) >= 20:
                            break
                
                except Exception as e:
                    print(f"跳过文件 {filename}: {e}")
        
        return np.array(all_segments[:20]) if all_segments else np.array([])
    
    def preprocess_signal(self, signal):
        """
        预处理信号
        """
        from scipy import signal as scipy_signal
        
        # 去除异常值
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        signal_clean = np.clip(signal, mean_val - 3*std_val, mean_val + 3*std_val)
        
        # 去趋势
        x = np.arange(len(signal_clean))
        coeffs = np.polyfit(x, signal_clean, 1)
        trend = np.polyval(coeffs, x)
        signal_detrend = signal_clean - trend
        
        # 带通滤波 (0.5-60Hz)
        sampling_rate = 128
        nyquist = sampling_rate / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist
        
        if low_freq < 1 and high_freq < 1:
            b, a = scipy_signal.butter(4, [low_freq, high_freq], btype='band')
            signal_filtered = scipy_signal.filtfilt(b, a, signal_detrend)
        else:
            signal_filtered = signal_detrend
        
        # 标准化
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / (np.std(signal_filtered) + 1e-8)
        
        return signal_normalized
    
    def create_signal_comparison_visualization(self):
        """
        创建信号对比可视化
        """
        print("创建信号对比可视化...")
        
        # 选择一些高置信度癫痫特征进行生成
        sample_epilepsy_features = self.high_conf_epilepsy_features[:10]
        sample_control_features = self.high_conf_control_features[:10]
        
        # 生成伪装的健康特征
        fake_healthy_features = self.generator.predict(sample_epilepsy_features, verbose=0)
        
        # 分类器预测
        epilepsy_pred = self.classifier.predict(sample_epilepsy_features, verbose=0)
        fake_pred = self.classifier.predict(fake_healthy_features, verbose=0)
        control_pred = self.classifier.predict(sample_control_features, verbose=0)
        
        # 创建可视化
        fig, axes = plt.subplots(3, 3, figsize=(18, 12))
        
        # 1. 特征对比 (前3个主成分)
        # PCA降维
        all_features = np.vstack([sample_epilepsy_features, fake_healthy_features, sample_control_features])
        pca = PCA(n_components=3)
        features_pca = pca.fit_transform(all_features)
        
        n_epilepsy = len(sample_epilepsy_features)
        n_fake = len(fake_healthy_features)
        
        epilepsy_pca = features_pca[:n_epilepsy]
        fake_pca = features_pca[n_epilepsy:n_epilepsy+n_fake]
        control_pca = features_pca[n_epilepsy+n_fake:]
        
        # PCA 2D可视化
        axes[0, 0].scatter(epilepsy_pca[:, 0], epilepsy_pca[:, 1], c='red', label='原始癫痫特征', alpha=0.7, s=50)
        axes[0, 0].scatter(fake_pca[:, 0], fake_pca[:, 1], c='orange', label='伪装健康特征', alpha=0.7, s=50)
        axes[0, 0].scatter(control_pca[:, 0], control_pca[:, 1], c='green', label='真实控制组特征', alpha=0.7, s=50)
        axes[0, 0].set_title('特征空间分布 (PCA)', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} 方差)')
        axes[0, 0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} 方差)')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 分类器预测概率对比
        categories = ['原始癫痫', '伪装健康', '真实控制组']
        epilepsy_probs = [np.mean(epilepsy_pred), np.mean(fake_pred), np.mean(control_pred)]
        
        bars = axes[0, 1].bar(categories, epilepsy_probs, color=['red', 'orange', 'green'], alpha=0.7)
        axes[0, 1].set_title('分类器预测概率对比', fontsize=12, fontweight='bold')
        axes[0, 1].set_ylabel('癫痫预测概率')
        axes[0, 1].set_ylim(0, 1)
        
        # 添加数值标签
        for bar, prob in zip(bars, epilepsy_probs):
            axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                           f'{prob:.3f}', ha='center', va='bottom', fontweight='bold')
        
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 特征差异热图
        feature_diff = np.abs(sample_epilepsy_features - fake_healthy_features)
        mean_diff = np.mean(feature_diff, axis=0)
        
        # 选择差异最大的前20个特征
        top_diff_indices = np.argsort(mean_diff)[-20:]
        
        im = axes[0, 2].imshow(feature_diff[:, top_diff_indices].T, cmap='Reds', aspect='auto')
        axes[0, 2].set_title('特征差异热图 (前20个差异最大特征)', fontsize=12, fontweight='bold')
        axes[0, 2].set_xlabel('样本索引')
        axes[0, 2].set_ylabel('特征索引')
        plt.colorbar(im, ax=axes[0, 2], shrink=0.8)
        
        # 4-6. 具体样本对比 (选择3个代表性样本)
        sample_indices = [0, 4, 8]  # 选择3个样本
        
        for i, idx in enumerate(sample_indices):
            ax = axes[1, i]
            
            # 原始癫痫特征
            ax.plot(sample_epilepsy_features[idx], 'r-', linewidth=2, label='原始癫痫特征', alpha=0.8)
            # 伪装健康特征
            ax.plot(fake_healthy_features[idx], 'g--', linewidth=2, label='伪装健康特征', alpha=0.8)
            
            ax.set_title(f'样本 {idx+1} 特征对比\n'
                        f'原始预测: {epilepsy_pred[idx][0]:.3f}, '
                        f'伪装预测: {fake_pred[idx][0]:.3f}', 
                        fontsize=10, fontweight='bold')
            ax.set_xlabel('特征维度')
            ax.set_ylabel('特征值')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        # 7-9. 特征分布对比
        feature_indices = [10, 30, 50]  # 选择3个代表性特征维度
        
        for i, feat_idx in enumerate(feature_indices):
            ax = axes[2, i]
            
            # 特征分布直方图
            ax.hist(sample_epilepsy_features[:, feat_idx], bins=15, alpha=0.6, 
                   color='red', label='原始癫痫', density=True)
            ax.hist(fake_healthy_features[:, feat_idx], bins=15, alpha=0.6, 
                   color='orange', label='伪装健康', density=True)
            ax.hist(sample_control_features[:, feat_idx], bins=15, alpha=0.6, 
                   color='green', label='真实控制组', density=True)
            
            ax.set_title(f'特征 {feat_idx} 分布对比', fontsize=10, fontweight='bold')
            ax.set_xlabel('特征值')
            ax.set_ylabel('密度')
            ax.legend()
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('癫痫信号与伪装信号对比分析.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("信号对比可视化完成!")
        
        return {
            'epilepsy_features': sample_epilepsy_features,
            'fake_healthy_features': fake_healthy_features,
            'control_features': sample_control_features,
            'epilepsy_pred': epilepsy_pred,
            'fake_pred': fake_pred,
            'control_pred': control_pred
        }


def main():
    """
    主函数
    """
    print("=" * 80)
    print("癫痫信号与伪装重建信号对比可视化")
    print("=" * 80)
    
    # 初始化可视化器
    visualizer = SignalComparisonVisualizer()
    
    # 加载模型和数据
    if not visualizer.load_models_and_data():
        print("❌ 模型和数据加载失败")
        return
    
    # 创建信号对比可视化
    comparison_results = visualizer.create_signal_comparison_visualization()
    
    print("\n" + "=" * 80)
    print("信号对比可视化完成!")
    print("=" * 80)
    print("主要发现:")
    print(f"- 原始癫痫特征平均预测概率: {np.mean(comparison_results['epilepsy_pred']):.3f}")
    print(f"- 伪装健康特征平均预测概率: {np.mean(comparison_results['fake_pred']):.3f}")
    print(f"- 真实控制组特征平均预测概率: {np.mean(comparison_results['control_pred']):.3f}")
    print(f"- 伪装效果: {(1 - np.mean(comparison_results['fake_pred'])) * 100:.1f}% 被识别为健康")
    
    return visualizer, comparison_results


if __name__ == "__main__":
    visualizer, results = main()
