#!/usr/bin/env python3
"""
Integrated EEG Preprocessing and Wavelet Analysis
Comparing raw vs preprocessed data for clinical epilepsy research
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import pywt
from scipy import signal
from scipy.stats import ttest_ind
import gzip
import os
import warnings
import mne
from mne.preprocessing import ICA

warnings.filterwarnings('ignore')
mne.set_log_level('WARNING')

# Set publication-quality plotting parameters
plt.rcParams.update({
    'figure.figsize': (16, 10),
    'font.size': 12,
    'axes.titlesize': 14,
    'axes.labelsize': 12,
    'xtick.labelsize': 10,
    'ytick.labelsize': 10,
    'legend.fontsize': 10,
    'figure.dpi': 300,
    'savefig.dpi': 300,
    'savefig.bbox': 'tight'
})

class IntegratedEEGAnalyzer:
    """
    Integrated EEG preprocessing and wavelet analysis
    """
    
    def __init__(self, sfreq=128):
        self.sfreq = sfreq
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Clinical frequency bands (adjusted for Nyquist limit)
        self.frequency_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 60)
        }
    
    def load_eeg_data(self, file_path, dataset_type='nigeria'):
        """
        Load EEG data and return both raw and preprocessed versions
        """
        try:
            print(f"Loading and preprocessing: {os.path.basename(file_path)}")
            
            # Load data
            with gzip.open(file_path, 'rt') as f:
                if dataset_type == 'nigeria':
                    df = pd.read_csv(f)
                    eeg_data = df[self.eeg_channels].apply(pd.to_numeric, errors='coerce').values.T
                    ch_names = self.eeg_channels
                else:
                    df = pd.read_csv(f, header=None)
                    eeg_data = pd.to_numeric(df.iloc[:, 1:15].values.flatten(), 
                                           errors='coerce').reshape(df.shape[0], 14).T
                    ch_names = [f'EEG_{i+1:02d}' for i in range(14)]
            
            # Clean data
            eeg_data = np.nan_to_num(eeg_data, nan=0.0).astype(float)
            
            # Create raw signal (first channel, convert to microvolts)
            raw_signal = eeg_data[0, :] - np.mean(eeg_data[0, :])
            
            # Create MNE Raw object for preprocessing
            info = mne.create_info(ch_names=ch_names, sfreq=self.sfreq, ch_types='eeg')
            raw_mne = mne.io.RawArray(eeg_data * 1e-6, info)  # Convert to volts
            
            if dataset_type == 'nigeria':
                montage = mne.channels.make_standard_montage('standard_1020')
                raw_mne.set_montage(montage, on_missing='ignore')
            
            # Apply preprocessing
            raw_preprocessed = raw_mne.copy()
            
            # Basic filtering
            raw_preprocessed.filter(l_freq=0.5, h_freq=60, verbose=False)
            raw_preprocessed.notch_filter(freqs=50, verbose=False)
            
            # Re-referencing
            raw_preprocessed.set_eeg_reference('average', projection=True, verbose=False)
            raw_preprocessed.apply_proj(verbose=False)
            
            # Simple ICA for artifact removal
            epochs = mne.make_fixed_length_epochs(raw_preprocessed, duration=2.0, preload=True, verbose=False)
            
            if len(epochs) > 10:
                ica = ICA(n_components=5, method='picard', random_state=42, verbose=False)
                ica.fit(epochs, verbose=False)
                
                # Remove high-variance components (likely artifacts)
                ica_data = ica.get_sources(epochs).get_data()
                component_vars = np.var(ica_data, axis=(0, 2))
                
                if len(component_vars) > 2:
                    artifact_idx = np.argsort(component_vars)[-2:]
                    ica.exclude = artifact_idx.tolist()
                    epochs_clean = ica.apply(epochs.copy(), verbose=False)
                    
                    # Convert back to continuous
                    epochs_df = epochs_clean.to_data_frame().reset_index()
                    eeg_columns = [col for col in epochs_df.columns if col in ch_names]
                    final_data = epochs_df[eeg_columns].values.T
                    
                    preprocessed_signal = final_data[0, :] * 1e6  # Convert back to microvolts
                else:
                    preprocessed_signal = raw_preprocessed.get_data()[0, :] * 1e6
            else:
                preprocessed_signal = raw_preprocessed.get_data()[0, :] * 1e6
            
            return raw_signal, preprocessed_signal
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None, None
    
    def wavelet_decomposition(self, signal_data, wavelet='db4', levels=8):
        """
        Perform wavelet decomposition and extract frequency bands
        """
        # Wavelet decomposition
        coeffs = pywt.wavedec(signal_data, wavelet, level=levels)
        
        # Extract frequency bands
        bands = {}
        
        # Gamma (30-60 Hz) - level 1
        gamma_coeffs = [np.zeros_like(coeffs[0])] + [coeffs[1]] + [np.zeros_like(c) for c in coeffs[2:]]
        bands['Gamma'] = pywt.waverec(gamma_coeffs, wavelet)
        
        # Beta (13-30 Hz) - level 2
        beta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(coeffs[1])] + [coeffs[2]] + [np.zeros_like(c) for c in coeffs[3:]]
        bands['Beta'] = pywt.waverec(beta_coeffs, wavelet)
        
        # Alpha (8-13 Hz) - level 3
        alpha_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:4]] + [coeffs[4]] + [np.zeros_like(c) for c in coeffs[5:]]
        bands['Alpha'] = pywt.waverec(alpha_coeffs, wavelet)
        
        # Theta (4-8 Hz) - level 4
        theta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:5]] + [coeffs[5]] + [np.zeros_like(c) for c in coeffs[6:]]
        bands['Theta'] = pywt.waverec(theta_coeffs, wavelet)
        
        # Delta (0.5-4 Hz) - levels 5+ and approximation
        delta_coeffs = [coeffs[0]] + [np.zeros_like(c) for c in coeffs[1:6]] + coeffs[6:]
        bands['Delta'] = pywt.waverec(delta_coeffs, wavelet)
        
        return bands
    
    def calculate_band_powers(self, bands):
        """
        Calculate relative power for each frequency band
        """
        powers = {}
        for band_name, band_signal in bands.items():
            powers[band_name] = np.mean(band_signal ** 2)
        return powers
    
    def analyze_sample(self, file_path, dataset_type='nigeria'):
        """
        Analyze a single EEG sample with both raw and preprocessed data
        """
        raw_signal, preprocessed_signal = self.load_eeg_data(file_path, dataset_type)
        
        if raw_signal is None or preprocessed_signal is None:
            return None
        
        # Ensure same length
        min_length = min(len(raw_signal), len(preprocessed_signal))
        raw_signal = raw_signal[:min_length]
        preprocessed_signal = preprocessed_signal[:min_length]
        
        # Wavelet analysis on raw data
        raw_bands = self.wavelet_decomposition(raw_signal)
        raw_powers = self.calculate_band_powers(raw_bands)
        
        # Wavelet analysis on preprocessed data
        prep_bands = self.wavelet_decomposition(preprocessed_signal)
        prep_powers = self.calculate_band_powers(prep_bands)
        
        return {
            'file_path': file_path,
            'raw_signal': raw_signal,
            'preprocessed_signal': preprocessed_signal,
            'raw_bands': raw_bands,
            'prep_bands': prep_bands,
            'raw_powers': raw_powers,
            'prep_powers': prep_powers
        }
    
    def plot_comparison(self, results, title="EEG Analysis Comparison", save_path=None):
        """
        Create comprehensive comparison plot
        """
        fig, axes = plt.subplots(3, 2, figsize=(16, 12))
        
        raw_signal = results['raw_signal']
        prep_signal = results['preprocessed_signal']
        
        # Time vectors (show first 10 seconds)
        max_samples = min(10 * self.sfreq, len(raw_signal))
        time = np.arange(max_samples) / self.sfreq
        
        # Time domain comparison
        axes[0, 0].plot(time, raw_signal[:max_samples], 'b-', linewidth=0.8, label='Raw')
        axes[0, 0].set_title('Raw EEG Signal', fontweight='bold')
        axes[0, 0].set_ylabel('Amplitude (μV)')
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].plot(time, prep_signal[:max_samples], 'r-', linewidth=0.8, label='Preprocessed')
        axes[0, 1].set_title('Preprocessed EEG Signal', fontweight='bold')
        axes[0, 1].set_ylabel('Amplitude (μV)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Power spectral density
        freqs_raw, psd_raw = signal.welch(raw_signal, fs=self.sfreq, nperseg=256)
        freqs_prep, psd_prep = signal.welch(prep_signal, fs=self.sfreq, nperseg=256)
        
        axes[1, 0].semilogy(freqs_raw, psd_raw, 'b-', linewidth=1)
        axes[1, 0].set_title('Raw PSD', fontweight='bold')
        axes[1, 0].set_ylabel('Power (μV²/Hz)')
        axes[1, 0].set_xlim(0, 60)
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].semilogy(freqs_prep, psd_prep, 'r-', linewidth=1)
        axes[1, 1].set_title('Preprocessed PSD', fontweight='bold')
        axes[1, 1].set_ylabel('Power (μV²/Hz)')
        axes[1, 1].set_xlim(0, 60)
        axes[1, 1].grid(True, alpha=0.3)
        
        # Frequency band power comparison
        band_names = list(self.frequency_bands.keys())
        raw_powers = [results['raw_powers'][band] for band in band_names]
        prep_powers = [results['prep_powers'][band] for band in band_names]
        
        x = np.arange(len(band_names))
        width = 0.35
        
        axes[2, 0].bar(x - width/2, raw_powers, width, label='Raw', color='blue', alpha=0.7)
        axes[2, 0].bar(x + width/2, prep_powers, width, label='Preprocessed', color='red', alpha=0.7)
        axes[2, 0].set_title('Frequency Band Power Comparison', fontweight='bold')
        axes[2, 0].set_ylabel('Relative Power')
        axes[2, 0].set_xticks(x)
        axes[2, 0].set_xticklabels(band_names)
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)
        
        # Power improvement ratios
        power_ratios = [prep_powers[i] / (raw_powers[i] + 1e-12) for i in range(len(band_names))]
        colors = ['green' if ratio < 1 else 'orange' for ratio in power_ratios]
        
        axes[2, 1].bar(band_names, power_ratios, color=colors, alpha=0.7)
        axes[2, 1].axhline(y=1, color='red', linestyle='--', label='No change')
        axes[2, 1].set_title('Power Ratio (Preprocessed/Raw)', fontweight='bold')
        axes[2, 1].set_ylabel('Ratio')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
        
        plt.suptitle(title, fontsize=16, fontweight='bold')
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Comparison plot saved: {save_path}")
        
        plt.show()


def main():
    """
    Main function for integrated analysis
    """
    print("Integrated EEG Preprocessing and Wavelet Analysis")
    print("=" * 60)
    
    analyzer = IntegratedEEGAnalyzer()
    
    # Analyze representative samples
    samples = [
        ('1252141/EEGs_Nigeria/signal-500-1.csv.gz', 'nigeria', 'Nigeria Epilepsy Patient'),
        ('1252141/EEGs_Nigeria/signal-6-1.csv.gz', 'nigeria', 'Nigeria Control Subject'),
    ]
    
    # Add Guinea-Bissau samples if metadata available
    if os.path.exists("1252141/metadata_guineabissau.csv"):
        metadata = pd.read_csv("1252141/metadata_guineabissau.csv")
        epilepsy_subject = metadata[metadata['Group'] == 'Epilepsy']['subject.id'].iloc[0]
        control_subject = metadata[metadata['Group'] == 'Control']['subject.id'].iloc[0]
        
        samples.extend([
            (f'1252141/EEGs_Guinea-Bissau/signal-{epilepsy_subject}.csv.gz', 'guinea_bissau', 'Guinea-Bissau Epilepsy Patient'),
            (f'1252141/EEGs_Guinea-Bissau/signal-{control_subject}.csv.gz', 'guinea_bissau', 'Guinea-Bissau Control Subject')
        ])
    
    # Analyze each sample
    all_results = []
    for file_path, dataset_type, title in samples:
        if os.path.exists(file_path):
            print(f"\nAnalyzing: {title}")
            results = analyzer.analyze_sample(file_path, dataset_type)
            
            if results:
                all_results.append((results, title))
                
                # Create comparison plot
                save_path = f"Integrated_Analysis_{title.replace(' ', '_')}.png"
                analyzer.plot_comparison(results, title, save_path)
                
                # Print summary
                print(f"Raw vs Preprocessed Power Comparison:")
                for band in analyzer.frequency_bands.keys():
                    raw_power = results['raw_powers'][band]
                    prep_power = results['prep_powers'][band]
                    ratio = prep_power / (raw_power + 1e-12)
                    print(f"  {band}: {raw_power:.2f} → {prep_power:.2f} (ratio: {ratio:.2f})")
    
    print(f"\nAnalysis completed! Generated {len(all_results)} comparison plots.")
    print("Files created:")
    for _, title in all_results:
        print(f"- Integrated_Analysis_{title.replace(' ', '_')}.png")


if __name__ == "__main__":
    main()
