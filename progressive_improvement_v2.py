#!/usr/bin/env python3
"""
渐进式改进 V2: 增加编码维度 + 优化平滑性损失
Progressive Improvement V2: Increase Encoding Dimension + Optimized Smoothness Loss

改进点:
- V1效果不佳，平滑度比值46.388太高
- 增加编码维度: 32 → 64 (减少信息瓶颈)
- 优化平滑性损失权重: 0.1 → 0.05 (减少过度约束)
- 添加二阶平滑性约束 (曲率约束)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import warnings
import os
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class ImprovedSmoothLoss(keras.losses.Loss):
    """
    改进的平滑性损失函数
    添加二阶平滑性约束（曲率约束）
    """
    def __init__(self, reconstruction_weight=1.0, smoothness_weight=0.05, 
                 curvature_weight=0.02, name="improved_smooth_loss"):
        super().__init__(name=name)
        self.reconstruction_weight = reconstruction_weight
        self.smoothness_weight = smoothness_weight
        self.curvature_weight = curvature_weight
    
    def call(self, y_true, y_pred):
        # 1. 重建损失 (Huber损失)
        reconstruction_loss = tf.keras.losses.huber(y_true, y_pred)
        
        # 2. 一阶平滑性损失 (相邻点差分)
        diff1_true = y_true[:, 1:] - y_true[:, :-1]
        diff1_pred = y_pred[:, 1:] - y_pred[:, :-1]
        smoothness_loss = tf.reduce_mean(tf.square(diff1_pred - diff1_true))
        
        # 3. 二阶平滑性损失 (曲率约束)
        diff2_true = diff1_true[:, 1:] - diff1_true[:, :-1]
        diff2_pred = diff1_pred[:, 1:] - diff1_pred[:, :-1]
        curvature_loss = tf.reduce_mean(tf.square(diff2_pred - diff2_true))
        
        # 4. 组合损失
        total_loss = (self.reconstruction_weight * reconstruction_loss + 
                     self.smoothness_weight * smoothness_loss +
                     self.curvature_weight * curvature_loss)
        
        return total_loss
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "reconstruction_weight": self.reconstruction_weight,
            "smoothness_weight": self.smoothness_weight,
            "curvature_weight": self.curvature_weight
        })
        return config

# 复用之前的组件
class AttentionGate(layers.Layer):
    def __init__(self, filters, **kwargs):
        super(AttentionGate, self).__init__(**kwargs)
        self.filters = filters
        self.W_g = layers.Dense(filters, activation='relu')
        self.W_x = layers.Dense(filters, activation='relu')
        self.psi = layers.Dense(1, activation='sigmoid')
        
    def call(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.psi(layers.Add()([g1, x1]))
        return layers.Multiply()([x, psi])
    
    def get_config(self):
        config = super().get_config()
        config.update({"filters": self.filters})
        return config

class ResidualBlock(layers.Layer):
    def __init__(self, filters, dropout_rate=0.1, **kwargs):
        super(ResidualBlock, self).__init__(**kwargs)
        self.filters = filters
        self.dropout_rate = dropout_rate
        
        self.dense1 = layers.Dense(filters, activation='relu')
        self.bn1 = layers.BatchNormalization()
        self.dropout1 = layers.Dropout(dropout_rate)
        
        self.dense2 = layers.Dense(filters, activation='relu')
        self.bn2 = layers.BatchNormalization()
        self.dropout2 = layers.Dropout(dropout_rate)
        
        self.shortcut = layers.Dense(filters, activation='linear')
        
    def call(self, inputs, training=None):
        x = self.dense1(inputs)
        x = self.bn1(x, training=training)
        x = self.dropout1(x, training=training)
        
        x = self.dense2(x)
        x = self.bn2(x, training=training)
        x = self.dropout2(x, training=training)
        
        shortcut = self.shortcut(inputs)
        return layers.Add()([x, shortcut])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "dropout_rate": self.dropout_rate
        })
        return config

class ProgressiveImprovementV2:
    """
    渐进式改进V2: 增加编码维度 + 优化平滑性损失
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.history = None
        
        print(f"渐进式改进V2 - 增加编码维度 + 优化平滑性损失:")
        print(f"- 分段长度: {segment_length} 样本点")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 改进点: 编码维度32→64, 平滑性权重0.1→0.05, 添加曲率约束")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """加载并预处理数据（保持不变）"""
        print("加载Gamma波数据...")
        
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        processed_waves = []
        
        for i, wave in enumerate(gamma_waves):
            # 基本预处理（保持原有逻辑）
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            processed_waves.append(wave_normalized)
        
        processed_waves = np.array(processed_waves)
        
        print(f"预处理完成: {processed_waves.shape}")
        return processed_waves, metadata
    
    def create_segments(self, processed_waves):
        """创建数据段（保持不变）"""
        print("创建数据段...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    if np.std(segment) > 0.01:
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        # 全局标准化
        self.scaler = RobustScaler()
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        segments_final = segments_scaled.reshape(segments.shape)
        
        print(f"段创建完成: {len(segments_final)} 个段")
        
        self.segments = segments_final
        self.segment_info = segment_info
        
        return segments_final, segment_info
    
    def build_improved_model_v2(self, input_dim=512, encoding_dim=64, base_filters=64):
        """
        构建改进的V2模型
        关键改进: 编码维度32→64, 优化损失函数
        """
        print("构建改进的V2模型...")
        
        # ==================== 架构改进：增加编码维度 ====================
        input_layer = keras.Input(shape=(input_dim,), name='input')
        
        # 编码器（保持不变）
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(input_layer)
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)
        
        # 瓶颈层 - 关键改进：编码维度32→64
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)
        
        # 解码器（保持不变）
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)
        
        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)
        
        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)
        
        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)
        
        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)
        
        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)
        
        output = layers.Dense(input_dim, activation='linear', name='output')(dec3_skip)
        
        # ==================== 关键改进：优化损失函数 ====================
        autoencoder = keras.Model(input_layer, output, name='improved_autoencoder_v2')
        encoder = keras.Model(input_layer, bottleneck, name='improved_encoder_v2')
        
        # 使用改进的平滑性损失函数
        improved_smooth_loss = ImprovedSmoothLoss(
            reconstruction_weight=1.0, 
            smoothness_weight=0.05,  # 减少平滑性权重
            curvature_weight=0.02    # 添加曲率约束
        )
        
        optimizer = keras.optimizers.Adam(learning_rate=0.0005)
        
        autoencoder.compile(
            optimizer=optimizer,
            loss=improved_smooth_loss,  # 关键改进点
            metrics=['mae', 'mse']
        )
        
        print("改进V2模型构建完成:")
        print(f"- 编码维度: 32 → {encoding_dim} (增加100%)")
        print(f"- 平滑性权重: 0.1 → 0.05 (减少50%)")
        print(f"- 曲率约束权重: 0.02 (新增)")
        print(f"- 总参数: {autoencoder.count_params():,}")
        
        self.autoencoder = autoencoder
        self.encoder = encoder
        
        return autoencoder, encoder

    def train_and_compare_v2(self, X_train, X_test, epochs=50):
        """
        训练V2模型并与V1对比
        """
        print("开始训练改进V2模型...")

        # 训练回调
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=8,
                min_lr=1e-6,
                verbose=1
            )
        ]

        # 训练
        history = self.autoencoder.fit(
            X_train, X_train,
            validation_split=0.2,
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        self.history = history

        # 评估和对比
        print("\n评估V2改进效果...")
        X_reconstructed = self.autoencoder.predict(X_test, verbose=0)

        # 计算评估指标
        mse_errors = np.mean((X_test - X_reconstructed) ** 2, axis=1)
        mae_errors = np.mean(np.abs(X_test - X_reconstructed), axis=1)

        # 计算平滑性指标
        smoothness_original = self.calculate_smoothness(X_test)
        smoothness_reconstructed = self.calculate_smoothness(X_test)

        # 相关系数
        correlations = []
        for i in range(len(X_test)):
            corr = np.corrcoef(X_test[i], X_reconstructed[i])[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        correlations = np.array(correlations)

        # 计算曲率指标（二阶差分方差）
        curvature_original = self.calculate_curvature(X_test)
        curvature_reconstructed = self.calculate_curvature(X_reconstructed)

        print(f"\n改进V2结果:")
        print(f"- MSE误差: {np.mean(mse_errors):.4f} ± {np.std(mse_errors):.4f}")
        print(f"- MAE误差: {np.mean(mae_errors):.4f} ± {np.std(mae_errors):.4f}")
        print(f"- 相关系数: {np.mean(correlations):.4f} ± {np.std(correlations):.4f}")
        print(f"- 原始信号平滑度: {np.mean(smoothness_original):.4f}")
        print(f"- 重建信号平滑度: {np.mean(smoothness_reconstructed):.4f}")
        print(f"- 平滑度比值: {np.mean(smoothness_reconstructed)/np.mean(smoothness_original):.4f}")
        print(f"- 原始信号曲率: {np.mean(curvature_original):.4f}")
        print(f"- 重建信号曲率: {np.mean(curvature_reconstructed):.4f}")
        print(f"- 曲率比值: {np.mean(curvature_reconstructed)/np.mean(curvature_original):.4f}")

        return {
            'mse_errors': mse_errors,
            'mae_errors': mae_errors,
            'correlations': correlations,
            'smoothness_original': smoothness_original,
            'smoothness_reconstructed': smoothness_reconstructed,
            'curvature_original': curvature_original,
            'curvature_reconstructed': curvature_reconstructed,
            'reconstructed': X_reconstructed
        }

    def calculate_smoothness(self, signals):
        """计算信号平滑度（一阶差分的方差）"""
        smoothness_scores = []
        for signal in signals:
            diff = np.diff(signal)
            smoothness = np.var(diff)
            smoothness_scores.append(smoothness)
        return np.array(smoothness_scores)

    def calculate_curvature(self, signals):
        """计算信号曲率（二阶差分的方差）"""
        curvature_scores = []
        for signal in signals:
            diff1 = np.diff(signal)
            diff2 = np.diff(diff1)
            curvature = np.var(diff2)
            curvature_scores.append(curvature)
        return np.array(curvature_scores)

    def visualize_v2_comparison(self, X_test, results, n_samples=3):
        """可视化V2对比结果"""
        print("创建V2对比可视化...")

        X_reconstructed = results['reconstructed']
        correlations = results['correlations']

        fig, axes = plt.subplots(n_samples, 1, figsize=(15, 4*n_samples))
        if n_samples == 1:
            axes = [axes]

        time = np.arange(512) / 128

        # 选择不同质量的重建样本
        best_idx = np.argmax(correlations)
        worst_idx = np.argmin(correlations)
        median_idx = np.argsort(correlations)[len(correlations)//2]

        samples = [
            (best_idx, f'最佳重建 (相关系数: {correlations[best_idx]:.3f})', 'green'),
            (median_idx, f'中等重建 (相关系数: {correlations[median_idx]:.3f})', 'blue'),
            (worst_idx, f'最差重建 (相关系数: {correlations[worst_idx]:.3f})', 'red')
        ]

        for i, (idx, title, color) in enumerate(samples):
            # 计算平滑度和曲率
            smooth_orig = self.calculate_smoothness([X_test[idx]])[0]
            smooth_recon = self.calculate_smoothness([X_reconstructed[idx]])[0]
            curv_orig = self.calculate_curvature([X_test[idx]])[0]
            curv_recon = self.calculate_curvature([X_reconstructed[idx]])[0]

            axes[i].plot(time, X_test[idx], 'k-', label='原始信号', linewidth=2, alpha=0.8)
            axes[i].plot(time, X_reconstructed[idx], '--', color=color, label='重建信号', linewidth=2)

            title_with_metrics = (f'{title}\n'
                                f'平滑度: 原始={smooth_orig:.4f}, 重建={smooth_recon:.4f} '
                                f'(比值={smooth_recon/smooth_orig:.2f})\n'
                                f'曲率: 原始={curv_orig:.4f}, 重建={curv_recon:.4f} '
                                f'(比值={curv_recon/curv_orig:.2f})')

            axes[i].set_title(title_with_metrics, fontsize=10, fontweight='bold')
            axes[i].set_xlabel('时间 (秒)')
            axes[i].set_ylabel('标准化幅值')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('改进V2对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("V2对比可视化完成!")


def test_v2_improvement():
    """
    测试V2改进效果
    """
    print("=" * 60)
    print("渐进式改进V2测试: 增加编码维度 + 优化平滑性损失")
    print("=" * 60)
    
    # 初始化
    pipeline = ProgressiveImprovementV2()
    
    # 加载数据
    processed_waves, metadata = pipeline.load_and_preprocess_data()
    segments, segment_info = pipeline.create_segments(processed_waves)
    
    # 划分数据
    X_train, X_test = train_test_split(segments, test_size=0.2, random_state=42)
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")
    
    # 构建模型
    autoencoder, encoder = pipeline.build_improved_model_v2(
        input_dim=512,
        encoding_dim=64,  # 关键改进：32→64
        base_filters=64
    )
    
    print("\n模型结构:")
    autoencoder.summary()

    # 训练和评估V2
    print("\n开始训练和评估V2...")
    results_v2 = pipeline.train_and_compare_v2(X_train, X_test, epochs=50)

    # 可视化V2对比
    pipeline.visualize_v2_comparison(X_test, results_v2, n_samples=3)

    # 保存V2模型（如果效果好）
    print("\n保存改进V2模型...")
    os.makedirs('progressive_models_v2', exist_ok=True)
    pipeline.autoencoder.save('progressive_models_v2/improved_autoencoder_v2.keras')

    import joblib
    joblib.dump(pipeline.scaler, 'progressive_models_v2/improved_scaler_v2.pkl')

    print("\n=" * 60)
    print("渐进式改进V2完成!")
    print("=" * 60)
    print("V2改进总结:")
    print("- ✅ 编码维度: 32 → 64 (减少信息瓶颈)")
    print("- ✅ 平滑性权重: 0.1 → 0.05 (减少过度约束)")
    print("- ✅ 添加曲率约束 (二阶平滑性)")
    print("- ✅ 量化评估平滑度和曲率改进")

    # V2决策建议
    smooth_ratio_v2 = np.mean(results_v2['smoothness_reconstructed']) / np.mean(results_v2['smoothness_original'])
    curv_ratio_v2 = np.mean(results_v2['curvature_reconstructed']) / np.mean(results_v2['curvature_original'])
    avg_correlation_v2 = np.mean(results_v2['correlations'])

    print(f"\nV2效果评估:")
    print(f"- 平滑度比值: {smooth_ratio_v2:.3f} (越接近1.0越好)")
    print(f"- 曲率比值: {curv_ratio_v2:.3f} (越接近1.0越好)")
    print(f"- 平均相关系数: {avg_correlation_v2:.3f} (越接近1.0越好)")

    # 与V1对比 (V1结果: 平滑度比值46.388, 相关系数0.956)
    print(f"\n与V1对比:")
    print(f"- 平滑度改进: 46.388 → {smooth_ratio_v2:.3f} (改善{((46.388-smooth_ratio_v2)/46.388*100):.1f}%)")
    print(f"- 相关系数变化: 0.956 → {avg_correlation_v2:.3f}")

    if smooth_ratio_v2 < 10.0 and avg_correlation_v2 > 0.85:
        print("✅ V2改进效果显著，建议保留此改进")
        recommendation_v2 = "KEEP"
    elif smooth_ratio_v2 < 20.0 and avg_correlation_v2 > 0.8:
        print("⚠️  V2改进效果一般，可以考虑进一步调整")
        recommendation_v2 = "ADJUST"
    else:
        print("❌ V2改进效果不佳，需要尝试其他方法")
        recommendation_v2 = "DISCARD"

    return pipeline, X_train, X_test, results_v2, recommendation_v2


if __name__ == "__main__":
    pipeline, X_train, X_test, results_v2, recommendation_v2 = test_v2_improvement()

    print(f"\n最终建议: {recommendation_v2}")

    if recommendation_v2 == "KEEP":
        print("🎉 V2改进成功！可以进行下一步改进。")
        print("💡 建议下一步: 尝试1D卷积层替代部分全连接层")
    elif recommendation_v2 == "ADJUST":
        print("🔧 V2需要调整，建议进一步优化参数。")
        print("💡 建议: 调整编码维度到128或优化损失函数权重")
    else:
        print("🔄 V2效果不佳，需要尝试其他改进方案。")
        print("💡 建议: 尝试完全不同的架构，如1D CNN或Transformer")
