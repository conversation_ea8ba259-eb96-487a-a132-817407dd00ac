#!/usr/bin/env python3
"""
渐进式改进 V3: 优化曲率控制 + 1D卷积层
Progressive Improvement V3: Optimize Curvature Control + 1D Convolution

改进点:
- V2平滑度完美(1.000)但曲率比值过高(23106)
- 优化曲率约束权重: 0.02 → 0.001 (减少过度约束)
- 引入1D卷积层替代部分全连接层 (更好的时序建模)
- 添加频域损失约束 (保持频谱特征)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import warnings
import os
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class AdvancedSmoothLoss(keras.losses.Loss):
    """
    高级平滑性损失函数
    优化曲率控制 + 频域约束
    """
    def __init__(self, reconstruction_weight=1.0, smoothness_weight=0.05, 
                 curvature_weight=0.001, frequency_weight=0.01, name="advanced_smooth_loss"):
        super().__init__(name=name)
        self.reconstruction_weight = reconstruction_weight
        self.smoothness_weight = smoothness_weight
        self.curvature_weight = curvature_weight  # 大幅减少
        self.frequency_weight = frequency_weight
    
    def call(self, y_true, y_pred):
        # 1. 重建损失 (Huber损失)
        reconstruction_loss = tf.keras.losses.huber(y_true, y_pred)
        
        # 2. 一阶平滑性损失
        diff1_true = y_true[:, 1:] - y_true[:, :-1]
        diff1_pred = y_pred[:, 1:] - y_pred[:, :-1]
        smoothness_loss = tf.reduce_mean(tf.square(diff1_pred - diff1_true))
        
        # 3. 二阶平滑性损失 (优化权重)
        diff2_true = diff1_true[:, 1:] - diff1_true[:, :-1]
        diff2_pred = diff1_pred[:, 1:] - diff1_pred[:, :-1]
        curvature_loss = tf.reduce_mean(tf.square(diff2_pred - diff2_true))
        
        # 4. 频域损失 (FFT频谱匹配)
        fft_true = tf.signal.fft(tf.cast(y_true, tf.complex64))
        fft_pred = tf.signal.fft(tf.cast(y_pred, tf.complex64))
        frequency_loss = tf.reduce_mean(tf.square(tf.abs(fft_true) - tf.abs(fft_pred)))
        
        # 5. 组合损失
        total_loss = (self.reconstruction_weight * reconstruction_loss + 
                     self.smoothness_weight * smoothness_loss +
                     self.curvature_weight * curvature_loss +
                     self.frequency_weight * frequency_loss)
        
        return total_loss
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "reconstruction_weight": self.reconstruction_weight,
            "smoothness_weight": self.smoothness_weight,
            "curvature_weight": self.curvature_weight,
            "frequency_weight": self.frequency_weight
        })
        return config

class Conv1DBlock(layers.Layer):
    """
    1D卷积块 - 更好的时序特征提取
    """
    def __init__(self, filters, kernel_size=3, dropout_rate=0.1, **kwargs):
        super(Conv1DBlock, self).__init__(**kwargs)
        self.filters = filters
        self.kernel_size = kernel_size
        self.dropout_rate = dropout_rate
        
        self.conv1d = layers.Conv1D(filters, kernel_size, padding='same', activation='relu')
        self.bn = layers.BatchNormalization()
        self.dropout = layers.Dropout(dropout_rate)
        
    def call(self, inputs, training=None):
        x = self.conv1d(inputs)
        x = self.bn(x, training=training)
        x = self.dropout(x, training=training)
        return x
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "kernel_size": self.kernel_size,
            "dropout_rate": self.dropout_rate
        })
        return config

# 复用之前的组件
class AttentionGate(layers.Layer):
    def __init__(self, filters, **kwargs):
        super(AttentionGate, self).__init__(**kwargs)
        self.filters = filters
        self.W_g = layers.Dense(filters, activation='relu')
        self.W_x = layers.Dense(filters, activation='relu')
        self.psi = layers.Dense(1, activation='sigmoid')
        
    def call(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.psi(layers.Add()([g1, x1]))
        return layers.Multiply()([x, psi])
    
    def get_config(self):
        config = super().get_config()
        config.update({"filters": self.filters})
        return config

class ResidualBlock(layers.Layer):
    def __init__(self, filters, dropout_rate=0.1, **kwargs):
        super(ResidualBlock, self).__init__(**kwargs)
        self.filters = filters
        self.dropout_rate = dropout_rate
        
        self.dense1 = layers.Dense(filters, activation='relu')
        self.bn1 = layers.BatchNormalization()
        self.dropout1 = layers.Dropout(dropout_rate)
        
        self.dense2 = layers.Dense(filters, activation='relu')
        self.bn2 = layers.BatchNormalization()
        self.dropout2 = layers.Dropout(dropout_rate)
        
        self.shortcut = layers.Dense(filters, activation='linear')
        
    def call(self, inputs, training=None):
        x = self.dense1(inputs)
        x = self.bn1(x, training=training)
        x = self.dropout1(x, training=training)
        
        x = self.dense2(x)
        x = self.bn2(x, training=training)
        x = self.dropout2(x, training=training)
        
        shortcut = self.shortcut(inputs)
        return layers.Add()([x, shortcut])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "dropout_rate": self.dropout_rate
        })
        return config

class ProgressiveImprovementV3:
    """
    渐进式改进V3: 优化曲率控制 + 1D卷积
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.history = None
        
        print(f"渐进式改进V3 - 优化曲率控制 + 1D卷积:")
        print(f"- 分段长度: {segment_length} 样本点")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 改进点: 曲率权重0.02→0.001, 1D卷积层, 频域约束")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """加载并预处理数据（保持不变）"""
        print("加载Gamma波数据...")
        
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        processed_waves = []
        
        for i, wave in enumerate(gamma_waves):
            # 基本预处理（保持原有逻辑）
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            processed_waves.append(wave_normalized)
        
        processed_waves = np.array(processed_waves)
        
        print(f"预处理完成: {processed_waves.shape}")
        return processed_waves, metadata
    
    def create_segments(self, processed_waves):
        """创建数据段（保持不变）"""
        print("创建数据段...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    if np.std(segment) > 0.01:
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        # 全局标准化
        self.scaler = RobustScaler()
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        segments_final = segments_scaled.reshape(segments.shape)
        
        print(f"段创建完成: {len(segments_final)} 个段")
        
        self.segments = segments_final
        self.segment_info = segment_info
        
        return segments_final, segment_info

    def build_hybrid_model_v3(self, input_dim=512, encoding_dim=64, base_filters=64):
        """
        构建混合架构V3模型
        关键改进: 1D卷积 + 优化损失函数
        """
        print("构建混合架构V3模型...")

        # ==================== 输入层 ====================
        input_layer = keras.Input(shape=(input_dim,), name='input')

        # 重塑为1D卷积格式 (batch, time_steps, features)
        reshaped = layers.Reshape((input_dim, 1), name='reshape_input')(input_layer)

        # ==================== 编码器部分 (混合架构) ====================
        # 1D卷积层 - 时序特征提取
        conv1 = Conv1DBlock(32, kernel_size=7, name='conv1d_block1')(reshaped)
        conv1 = layers.MaxPooling1D(2, name='maxpool1')(conv1)  # 512 -> 256

        conv2 = Conv1DBlock(64, kernel_size=5, name='conv1d_block2')(conv1)
        conv2 = layers.MaxPooling1D(2, name='maxpool2')(conv2)  # 256 -> 128

        conv3 = Conv1DBlock(128, kernel_size=3, name='conv1d_block3')(conv2)
        conv3 = layers.MaxPooling1D(2, name='maxpool3')(conv3)  # 128 -> 64

        # 展平并转换为全连接
        flattened = layers.Flatten(name='flatten')(conv3)

        # 残差块编码
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(flattened)
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)

        # 瓶颈层
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)

        # ==================== 解码器部分 (混合架构) ====================
        # 全连接解码
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)

        # 跳跃连接1
        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)

        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)

        # 跳跃连接2
        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)

        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)

        # 跳跃连接3
        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)

        # 重塑为1D卷积格式进行上采样
        reshaped_dec = layers.Dense(64*128, activation='relu', name='reshape_dense')(dec3_skip)
        reshaped_dec = layers.Reshape((64, 128), name='reshape_for_upsampling')(reshaped_dec)

        # 1D转置卷积上采样
        upconv1 = layers.Conv1DTranspose(64, 3, strides=2, padding='same', activation='relu', name='upconv1')(reshaped_dec)  # 64 -> 128
        upconv2 = layers.Conv1DTranspose(32, 5, strides=2, padding='same', activation='relu', name='upconv2')(upconv1)  # 128 -> 256
        upconv3 = layers.Conv1DTranspose(16, 7, strides=2, padding='same', activation='relu', name='upconv3')(upconv2)  # 256 -> 512

        # 最终输出层
        output_conv = layers.Conv1D(1, 1, padding='same', activation='linear', name='output_conv')(upconv3)
        output = layers.Flatten(name='output')(output_conv)

        # ==================== 构建模型 ====================
        autoencoder = keras.Model(input_layer, output, name='hybrid_autoencoder_v3')
        encoder = keras.Model(input_layer, bottleneck, name='hybrid_encoder_v3')

        # ==================== 关键改进：高级损失函数 ====================
        advanced_smooth_loss = AdvancedSmoothLoss(
            reconstruction_weight=1.0,
            smoothness_weight=0.05,
            curvature_weight=0.001,  # 大幅减少曲率权重
            frequency_weight=0.01    # 添加频域约束
        )

        optimizer = keras.optimizers.Adam(learning_rate=0.0005)

        autoencoder.compile(
            optimizer=optimizer,
            loss=advanced_smooth_loss,  # 关键改进点
            metrics=['mae', 'mse']
        )

        print("混合架构V3模型构建完成:")
        print(f"- 架构: 1D卷积 + 全连接混合")
        print(f"- 编码维度: {encoding_dim}")
        print(f"- 曲率权重: 0.02 → 0.001 (减少95%)")
        print(f"- 频域约束权重: 0.01 (新增)")
        print(f"- 总参数: {autoencoder.count_params():,}")

        self.autoencoder = autoencoder
        self.encoder = encoder

        return autoencoder, encoder

    def train_and_compare_v3(self, X_train, X_test, epochs=50):
        """训练V3模型并与V2对比"""
        print("开始训练混合架构V3模型...")

        # 训练回调
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=8,
                min_lr=1e-6,
                verbose=1
            )
        ]

        # 训练
        history = self.autoencoder.fit(
            X_train, X_train,
            validation_split=0.2,
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        self.history = history

        # 评估和对比
        print("\n评估V3改进效果...")
        X_reconstructed = self.autoencoder.predict(X_test, verbose=0)

        # 计算评估指标
        mse_errors = np.mean((X_test - X_reconstructed) ** 2, axis=1)
        mae_errors = np.mean(np.abs(X_test - X_reconstructed), axis=1)

        # 计算平滑性和曲率指标
        smoothness_original = self.calculate_smoothness(X_test)
        smoothness_reconstructed = self.calculate_smoothness(X_reconstructed)
        curvature_original = self.calculate_curvature(X_test)
        curvature_reconstructed = self.calculate_curvature(X_reconstructed)

        # 相关系数
        correlations = []
        for i in range(len(X_test)):
            corr = np.corrcoef(X_test[i], X_reconstructed[i])[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        correlations = np.array(correlations)

        # 频域相似性
        frequency_similarities = self.calculate_frequency_similarity(X_test, X_reconstructed)

        print(f"\n混合架构V3结果:")
        print(f"- MSE误差: {np.mean(mse_errors):.4f} ± {np.std(mse_errors):.4f}")
        print(f"- MAE误差: {np.mean(mae_errors):.4f} ± {np.std(mae_errors):.4f}")
        print(f"- 相关系数: {np.mean(correlations):.4f} ± {np.std(correlations):.4f}")
        print(f"- 平滑度比值: {np.mean(smoothness_reconstructed)/np.mean(smoothness_original):.4f}")
        print(f"- 曲率比值: {np.mean(curvature_reconstructed)/np.mean(curvature_original):.4f}")
        print(f"- 频域相似性: {np.mean(frequency_similarities):.4f} ± {np.std(frequency_similarities):.4f}")

        return {
            'mse_errors': mse_errors,
            'mae_errors': mae_errors,
            'correlations': correlations,
            'smoothness_original': smoothness_original,
            'smoothness_reconstructed': smoothness_reconstructed,
            'curvature_original': curvature_original,
            'curvature_reconstructed': curvature_reconstructed,
            'frequency_similarities': frequency_similarities,
            'reconstructed': X_reconstructed
        }

    def calculate_smoothness(self, signals):
        """计算信号平滑度（一阶差分的方差）"""
        smoothness_scores = []
        for signal in signals:
            diff = np.diff(signal)
            smoothness = np.var(diff)
            smoothness_scores.append(smoothness)
        return np.array(smoothness_scores)

    def calculate_curvature(self, signals):
        """计算信号曲率（二阶差分的方差）"""
        curvature_scores = []
        for signal in signals:
            diff1 = np.diff(signal)
            diff2 = np.diff(diff1)
            curvature = np.var(diff2)
            curvature_scores.append(curvature)
        return np.array(curvature_scores)

    def calculate_frequency_similarity(self, signals_true, signals_pred):
        """计算频域相似性"""
        similarities = []
        for i in range(len(signals_true)):
            fft_true = np.fft.fft(signals_true[i])
            fft_pred = np.fft.fft(signals_pred[i])

            # 计算幅度谱相关系数
            mag_true = np.abs(fft_true)
            mag_pred = np.abs(fft_pred)

            corr = np.corrcoef(mag_true, mag_pred)[0, 1]
            if not np.isnan(corr):
                similarities.append(corr)
            else:
                similarities.append(0.0)

        return np.array(similarities)


def test_v3_improvement():
    """
    测试V3改进效果
    """
    print("=" * 60)
    print("渐进式改进V3测试: 优化曲率控制 + 1D卷积")
    print("=" * 60)
    
    # 初始化
    pipeline = ProgressiveImprovementV3()
    
    # 加载数据
    processed_waves, metadata = pipeline.load_and_preprocess_data()
    segments, segment_info = pipeline.create_segments(processed_waves)
    
    # 划分数据
    X_train, X_test = train_test_split(segments, test_size=0.2, random_state=42)
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")

    # 构建V3模型
    autoencoder, encoder = pipeline.build_hybrid_model_v3(
        input_dim=512,
        encoding_dim=64,
        base_filters=64
    )

    print("\n混合架构V3模型结构:")
    autoencoder.summary()

    # 训练和评估V3
    print("\n开始训练和评估V3...")
    results_v3 = pipeline.train_and_compare_v3(X_train, X_test, epochs=50)

    # 保存V3模型
    print("\n保存混合架构V3模型...")
    os.makedirs('progressive_models_v3', exist_ok=True)
    pipeline.autoencoder.save('progressive_models_v3/hybrid_autoencoder_v3.keras')

    import joblib
    joblib.dump(pipeline.scaler, 'progressive_models_v3/hybrid_scaler_v3.pkl')

    print("\n=" * 60)
    print("渐进式改进V3完成!")
    print("=" * 60)
    print("V3改进总结:")
    print("- ✅ 混合架构: 1D卷积 + 全连接")
    print("- ✅ 曲率权重: 0.02 → 0.001 (减少95%)")
    print("- ✅ 频域约束: 0.01 (新增)")
    print("- ✅ 时序建模: 1D卷积层")

    # V3决策建议
    smooth_ratio_v3 = np.mean(results_v3['smoothness_reconstructed']) / np.mean(results_v3['smoothness_original'])
    curv_ratio_v3 = np.mean(results_v3['curvature_reconstructed']) / np.mean(results_v3['curvature_original'])
    avg_correlation_v3 = np.mean(results_v3['correlations'])
    avg_frequency_sim_v3 = np.mean(results_v3['frequency_similarities'])

    print(f"\nV3效果评估:")
    print(f"- 平滑度比值: {smooth_ratio_v3:.3f} (目标: ~1.0)")
    print(f"- 曲率比值: {curv_ratio_v3:.3f} (目标: ~1.0)")
    print(f"- 相关系数: {avg_correlation_v3:.3f} (目标: >0.9)")
    print(f"- 频域相似性: {avg_frequency_sim_v3:.3f} (目标: >0.8)")

    # 与V2对比 (V2结果: 平滑度1.000, 曲率23106, 相关系数0.954)
    print(f"\n与V2对比:")
    print(f"- 平滑度: 1.000 → {smooth_ratio_v3:.3f}")
    print(f"- 曲率改进: 23106 → {curv_ratio_v3:.3f} (改善{((23106-curv_ratio_v3)/23106*100):.1f}%)")
    print(f"- 相关系数: 0.954 → {avg_correlation_v3:.3f}")
    print(f"- 频域相似性: 新增 → {avg_frequency_sim_v3:.3f}")

    # 综合评估
    if (smooth_ratio_v3 < 2.0 and curv_ratio_v3 < 10.0 and
        avg_correlation_v3 > 0.9 and avg_frequency_sim_v3 > 0.8):
        print("✅ V3改进效果优秀，所有指标都达到目标")
        recommendation_v3 = "EXCELLENT"
    elif (smooth_ratio_v3 < 3.0 and curv_ratio_v3 < 50.0 and
          avg_correlation_v3 > 0.85):
        print("✅ V3改进效果良好，建议保留此改进")
        recommendation_v3 = "KEEP"
    elif (smooth_ratio_v3 < 5.0 and curv_ratio_v3 < 100.0 and
          avg_correlation_v3 > 0.8):
        print("⚠️  V3改进效果一般，可以考虑进一步调整")
        recommendation_v3 = "ADJUST"
    else:
        print("❌ V3改进效果不佳，需要尝试其他方法")
        recommendation_v3 = "DISCARD"

    return pipeline, X_train, X_test, results_v3, recommendation_v3


if __name__ == "__main__":
    pipeline, X_train, X_test, results_v3, recommendation_v3 = test_v3_improvement()

    print(f"\n最终建议: {recommendation_v3}")

    if recommendation_v3 == "EXCELLENT":
        print("🎉 V3改进非常成功！已达到最佳效果。")
        print("💡 可以考虑: 微调超参数或尝试更大的数据集")
    elif recommendation_v3 == "KEEP":
        print("🎉 V3改进成功！可以进行下一步优化。")
        print("💡 建议下一步: 尝试Transformer架构或更精细的损失函数")
    elif recommendation_v3 == "ADJUST":
        print("🔧 V3需要调整，建议进一步优化参数。")
        print("💡 建议: 调整损失函数权重或网络架构")
    else:
        print("🔄 V3效果不佳，需要尝试其他改进方案。")
        print("💡 建议: 回到V2基础上尝试其他方向")
