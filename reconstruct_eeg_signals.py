#!/usr/bin/env python3
"""
EEG信号重建与对比可视化
EEG Signal Reconstruction and Comparison Visualization

功能:
1. 从伪装特征重建完整的EEG信号
2. 对比原始癫痫EEG信号 vs 伪装重建EEG信号
3. 展示病灶掩盖在时域信号上的效果
4. 分析信号的时频特性变化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import RobustScaler
import warnings
import os
import gzip
import json
import joblib
import pywt
from scipy import signal as scipy_signal
from scipy.stats import skew, kurtosis
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class EEGSignalReconstructor:
    """
    EEG信号重建器
    """
    
    def __init__(self, segment_length=512, sampling_rate=128):
        self.segment_length = segment_length
        self.sampling_rate = sampling_rate
        
        # 模型和标准化器
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        # 特征提取器 (用于逆向重建)
        self.wavelet_extractor = None
        
        print(f"EEG信号重建器初始化:")
        print(f"- 信号长度: {segment_length} 样本点 (4秒)")
        print(f"- 采样率: {sampling_rate} Hz")
    
    def load_models_and_data(self):
        """
        加载模型和数据
        """
        print("加载模型和数据...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        return True
    
    def load_original_eeg_signals(self, num_samples=5):
        """
        加载原始EEG信号段
        """
        print(f"加载原始EEG信号段 (FC5通道, {num_samples}个样本)...")
        
        # 加载Nigeria数据集元数据
        nigeria_metadata_path = "1252141/metadata_nigeria.csv"
        metadata = pd.read_csv(nigeria_metadata_path)
        
        # 获取癫痫组文件
        epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        
        original_signals = []
        original_features = []
        
        for filename in epilepsy_files[:10]:  # 多加载一些以确保有足够样本
            file_path = os.path.join("1252141/EEGs_Nigeria", filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                    
                    if 'FC5' in df.columns:
                        channel_data = df['FC5'].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values
                        
                        # 预处理
                        channel_data = self.preprocess_signal(channel_data)
                        
                        # 创建段
                        step_size = self.segment_length // 4  # 75%重叠
                        for i in range(0, len(channel_data) - self.segment_length + 1, step_size):
                            segment = channel_data[i:i + self.segment_length]
                            
                            if np.std(segment) > 0.01:  # 过滤低变异段
                                # 标准化信号
                                segment_scaled = self.signal_scaler.transform(segment.reshape(-1, 1)).flatten()
                                
                                # 提取特征
                                features = self.extract_features_from_signal(segment_scaled)
                                
                                original_signals.append(segment_scaled)
                                original_features.append(features)
                                
                                if len(original_signals) >= num_samples:
                                    break
                        
                        if len(original_signals) >= num_samples:
                            break
                
                except Exception as e:
                    print(f"跳过文件 {filename}: {e}")
        
        original_signals = np.array(original_signals[:num_samples])
        original_features = np.array(original_features[:num_samples])
        
        print(f"原始EEG信号加载完成:")
        print(f"- 信号段数: {len(original_signals)}")
        print(f"- 信号形状: {original_signals.shape}")
        print(f"- 特征形状: {original_features.shape}")
        
        return original_signals, original_features
    
    def preprocess_signal(self, signal):
        """
        预处理信号
        """
        # 去除异常值
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        signal_clean = np.clip(signal, mean_val - 3*std_val, mean_val + 3*std_val)
        
        # 去趋势
        x = np.arange(len(signal_clean))
        coeffs = np.polyfit(x, signal_clean, 1)
        trend = np.polyval(coeffs, x)
        signal_detrend = signal_clean - trend
        
        # 带通滤波 (0.5-60Hz)
        nyquist = self.sampling_rate / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist
        
        if low_freq < 1 and high_freq < 1:
            b, a = scipy_signal.butter(4, [low_freq, high_freq], btype='band')
            signal_filtered = scipy_signal.filtfilt(b, a, signal_detrend)
        else:
            signal_filtered = signal_detrend
        
        # 标准化
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / (np.std(signal_filtered) + 1e-8)
        
        return signal_normalized
    
    def extract_features_from_signal(self, signal):
        """
        从信号提取特征 (简化版，与原始特征提取保持一致)
        """
        # 小波特征
        wavelet_features = self.extract_wavelet_features(signal)
        
        # 时域特征
        time_features = self.extract_time_domain_features(signal)
        
        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal)
        
        # 拼接特征
        all_features = np.hstack([wavelet_features, time_features, freq_features])
        
        # 标准化
        features_scaled = self.feature_scaler.transform(all_features.reshape(1, -1)).flatten()
        
        return features_scaled
    
    def extract_wavelet_features(self, signal):
        """
        提取小波特征 (简化版)
        """
        # 小波分解
        coeffs = pywt.wavedec(signal, 'db4', level=5)
        
        features = []
        
        # 近似系数
        cA = coeffs[0]
        features.extend([
            np.mean(cA), np.std(cA), np.var(cA),
            np.max(cA), np.min(cA),
            np.percentile(cA, 25), np.percentile(cA, 75),
            np.sum(cA ** 2),  # 能量
            -np.sum(np.abs(cA) * np.log2(np.abs(cA) + 1e-12))  # 熵
        ])
        
        # 细节系数
        for cD in coeffs[1:]:
            features.extend([
                np.mean(cD), np.std(cD), np.var(cD),
                np.max(cD), np.min(cD),
                np.percentile(cD, 25), np.percentile(cD, 75),
                np.sum(cD ** 2),  # 能量
                -np.sum(np.abs(cD) * np.log2(np.abs(cD) + 1e-12)),  # 熵
                np.sum(np.diff(np.sign(cD)) != 0) / len(cD)  # 过零率
            ])
        
        # 能量比
        energies = [np.sum(c ** 2) for c in coeffs]
        total_energy = sum(energies)
        energy_ratios = [e/total_energy for e in energies]
        features.extend(energy_ratios)
        
        # 相对小波能量
        features.extend([
            energies[0] / energies[1] if energies[1] > 0 else 0,
            max(energies[1:]) / energies[0] if energies[0] > 0 else 0
        ])
        
        return np.array(features)
    
    def extract_time_domain_features(self, signal):
        """
        提取时域特征
        """
        from scipy import stats
        
        features = [
            np.mean(signal), np.std(signal), np.var(signal),
            np.max(signal), np.min(signal), np.ptp(signal),
            np.percentile(signal, 25), np.percentile(signal, 75),
            np.median(signal), np.mean(np.abs(signal)),
            np.sqrt(np.mean(signal**2)),
            len(signal[signal > 0]) / len(signal),
            np.sum(np.diff(signal) > 0) / len(signal),
            stats.kurtosis(signal), stats.skew(signal)
        ]
        
        return np.array(features)
    
    def extract_frequency_domain_features(self, signal):
        """
        提取频域特征
        """
        # FFT
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/self.sampling_rate)
        psd = np.abs(fft) ** 2
        
        # 频带划分
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        band_powers = []
        
        for low, high in bands:
            band_mask = (freqs >= low) & (freqs <= high)
            band_power = np.sum(psd[band_mask])
            band_powers.append(band_power)
        
        total_power = np.sum(band_powers)
        
        features = [
            np.mean(psd), np.std(psd), np.max(psd),
            freqs[np.argmax(psd)], total_power
        ]
        
        # 相对频带功率
        if total_power > 0:
            features.extend([p/total_power for p in band_powers])
        else:
            features.extend([0] * len(band_powers))
        
        # 频带功率比
        if band_powers[1] > 0:
            features.append(band_powers[0] / band_powers[1])
        else:
            features.append(0)
        
        if band_powers[0] > 0:
            features.append(band_powers[2] / band_powers[0])
        else:
            features.append(0)
        
        return np.array(features)

    def reconstruct_signals_from_features(self, features):
        """
        从特征重建EEG信号 (逆向工程)
        """
        print("从特征重建EEG信号...")

        reconstructed_signals = []

        for feature_vector in features:
            # 这是一个近似重建过程，因为从特征到信号的映射不是唯一的
            # 我们使用小波逆变换和频域合成的组合方法

            # 从特征中提取小波系数信息
            reconstructed_signal = self.inverse_feature_to_signal(feature_vector)

            reconstructed_signals.append(reconstructed_signal)

        return np.array(reconstructed_signals)

    def inverse_feature_to_signal(self, features):
        """
        从单个特征向量重建信号 (近似方法)
        """
        # 这是一个启发式方法，基于特征的统计信息重建信号

        # 提取时域统计特征 (假设特征顺序与提取时一致)
        # 小波特征: 0-66, 时域特征: 67-81, 频域特征: 82-93

        time_features_start = 67
        freq_features_start = 82

        # 提取时域统计信息
        mean_val = features[time_features_start]
        std_val = features[time_features_start + 1]

        # 提取频域信息
        freq_features = features[freq_features_start:freq_features_start + 12]

        # 基于频域特征重建信号的频谱
        freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)

        # 构建功率谱密度
        psd = np.zeros(len(freqs))

        # 频带功率 (Delta, Theta, Alpha, Beta, Gamma)
        band_powers = freq_features[5:10]  # 相对频带功率
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]

        for i, (low, high) in enumerate(bands):
            band_mask = (np.abs(freqs) >= low) & (np.abs(freqs) <= high)
            psd[band_mask] = band_powers[i]

        # 添加一些随机相位
        phases = np.random.uniform(0, 2*np.pi, len(freqs))

        # 构建复数频谱
        spectrum = np.sqrt(psd) * np.exp(1j * phases)

        # 确保共轭对称性 (实信号的要求)
        spectrum[len(freqs)//2+1:] = np.conj(spectrum[1:len(freqs)//2][::-1])

        # 逆FFT得到时域信号
        reconstructed = np.real(np.fft.ifft(spectrum))

        # 调整统计特性
        reconstructed = (reconstructed - np.mean(reconstructed)) / (np.std(reconstructed) + 1e-8)
        reconstructed = reconstructed * std_val + mean_val

        return reconstructed

    def create_signal_reconstruction_comparison(self, original_signals, original_features):
        """
        创建信号重建对比可视化
        """
        print("创建信号重建对比可视化...")

        # 1. 生成伪装特征
        fake_healthy_features = self.generator.predict(original_features, verbose=0)

        # 2. 从伪装特征重建信号
        reconstructed_signals = self.reconstruct_signals_from_features(fake_healthy_features)

        # 3. 分类器预测
        original_pred = self.classifier.predict(original_features, verbose=0)
        fake_pred = self.classifier.predict(fake_healthy_features, verbose=0)

        # 4. 创建对比可视化
        n_samples = len(original_signals)
        fig, axes = plt.subplots(n_samples, 3, figsize=(18, 4*n_samples))

        if n_samples == 1:
            axes = axes.reshape(1, -1)

        time_axis = np.arange(self.segment_length) / self.sampling_rate

        for i in range(n_samples):
            # 原始癫痫信号
            axes[i, 0].plot(time_axis, original_signals[i], 'r-', linewidth=1.5, alpha=0.8)
            axes[i, 0].set_title(f'样本 {i+1}: 原始癫痫信号\n'
                                f'分类器预测: {original_pred[i][0]:.3f} (癫痫概率)',
                                fontsize=12, fontweight='bold')
            axes[i, 0].set_xlabel('时间 (秒)')
            axes[i, 0].set_ylabel('标准化幅值')
            axes[i, 0].grid(True, alpha=0.3)
            axes[i, 0].set_ylim(-4, 4)

            # 伪装重建信号
            axes[i, 1].plot(time_axis, reconstructed_signals[i], 'g-', linewidth=1.5, alpha=0.8)
            axes[i, 1].set_title(f'样本 {i+1}: 伪装重建信号\n'
                                f'分类器预测: {fake_pred[i][0]:.3f} (癫痫概率)',
                                fontsize=12, fontweight='bold')
            axes[i, 1].set_xlabel('时间 (秒)')
            axes[i, 1].set_ylabel('标准化幅值')
            axes[i, 1].grid(True, alpha=0.3)
            axes[i, 1].set_ylim(-4, 4)

            # 信号对比
            axes[i, 2].plot(time_axis, original_signals[i], 'r-', linewidth=1.5,
                           alpha=0.7, label='原始癫痫信号')
            axes[i, 2].plot(time_axis, reconstructed_signals[i], 'g--', linewidth=1.5,
                           alpha=0.7, label='伪装重建信号')
            axes[i, 2].set_title(f'样本 {i+1}: 信号对比\n'
                                f'伪装效果: {(1-fake_pred[i][0])*100:.1f}% 被识别为健康',
                                fontsize=12, fontweight='bold')
            axes[i, 2].set_xlabel('时间 (秒)')
            axes[i, 2].set_ylabel('标准化幅值')
            axes[i, 2].legend()
            axes[i, 2].grid(True, alpha=0.3)
            axes[i, 2].set_ylim(-4, 4)

        plt.tight_layout()
        plt.savefig('癫痫信号vs伪装重建信号对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 5. 创建频谱对比
        self.create_frequency_comparison(original_signals, reconstructed_signals,
                                       original_pred, fake_pred)

        # 6. 创建统计特性对比
        self.create_statistical_comparison(original_signals, reconstructed_signals,
                                         original_features, fake_healthy_features)

        print("信号重建对比可视化完成!")

        return {
            'original_signals': original_signals,
            'reconstructed_signals': reconstructed_signals,
            'original_features': original_features,
            'fake_features': fake_healthy_features,
            'original_pred': original_pred,
            'fake_pred': fake_pred
        }

    def create_frequency_comparison(self, original_signals, reconstructed_signals,
                                  original_pred, fake_pred):
        """
        创建频谱对比
        """
        print("创建频谱对比...")

        n_samples = len(original_signals)
        fig, axes = plt.subplots(n_samples, 2, figsize=(15, 4*n_samples))

        if n_samples == 1:
            axes = axes.reshape(1, -1)

        freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)[:self.segment_length//2]

        for i in range(n_samples):
            # 计算功率谱密度
            original_fft = np.fft.fft(original_signals[i])
            original_psd = np.abs(original_fft[:self.segment_length//2]) ** 2

            reconstructed_fft = np.fft.fft(reconstructed_signals[i])
            reconstructed_psd = np.abs(reconstructed_fft[:self.segment_length//2]) ** 2

            # 功率谱对比
            axes[i, 0].semilogy(freqs, original_psd, 'r-', linewidth=2,
                               alpha=0.7, label='原始癫痫信号')
            axes[i, 0].semilogy(freqs, reconstructed_psd, 'g--', linewidth=2,
                               alpha=0.7, label='伪装重建信号')
            axes[i, 0].set_title(f'样本 {i+1}: 功率谱密度对比', fontsize=12, fontweight='bold')
            axes[i, 0].set_xlabel('频率 (Hz)')
            axes[i, 0].set_ylabel('功率谱密度')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)
            axes[i, 0].set_xlim(0, 60)

            # 频带能量对比
            bands = ['Delta\n(0.5-4Hz)', 'Theta\n(4-8Hz)', 'Alpha\n(8-13Hz)',
                    'Beta\n(13-30Hz)', 'Gamma\n(30-60Hz)']
            band_ranges = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]

            original_band_powers = []
            reconstructed_band_powers = []

            for low, high in band_ranges:
                band_mask = (freqs >= low) & (freqs <= high)
                original_band_powers.append(np.sum(original_psd[band_mask]))
                reconstructed_band_powers.append(np.sum(reconstructed_psd[band_mask]))

            x = np.arange(len(bands))
            width = 0.35

            axes[i, 1].bar(x - width/2, original_band_powers, width,
                          label='原始癫痫信号', color='red', alpha=0.7)
            axes[i, 1].bar(x + width/2, reconstructed_band_powers, width,
                          label='伪装重建信号', color='green', alpha=0.7)

            axes[i, 1].set_title(f'样本 {i+1}: 频带能量对比', fontsize=12, fontweight='bold')
            axes[i, 1].set_xlabel('频带')
            axes[i, 1].set_ylabel('能量')
            axes[i, 1].set_xticks(x)
            axes[i, 1].set_xticklabels(bands)
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('癫痫信号vs伪装信号频谱对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("频谱对比完成!")

    def create_statistical_comparison(self, original_signals, reconstructed_signals,
                                    original_features, fake_features):
        """
        创建统计特性对比
        """
        print("创建统计特性对比...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 信号统计特性对比
        original_stats = {
            '均值': [np.mean(sig) for sig in original_signals],
            '标准差': [np.std(sig) for sig in original_signals],
            '偏度': [skew(sig) for sig in original_signals],
            '峰度': [kurtosis(sig) for sig in original_signals]
        }

        reconstructed_stats = {
            '均值': [np.mean(sig) for sig in reconstructed_signals],
            '标准差': [np.std(sig) for sig in reconstructed_signals],
            '偏度': [skew(sig) for sig in reconstructed_signals],
            '峰度': [kurtosis(sig) for sig in reconstructed_signals]
        }

        stats_names = list(original_stats.keys())
        x = np.arange(len(stats_names))
        width = 0.35

        original_means = [np.mean(original_stats[stat]) for stat in stats_names]
        reconstructed_means = [np.mean(reconstructed_stats[stat]) for stat in stats_names]

        axes[0, 0].bar(x - width/2, original_means, width,
                      label='原始癫痫信号', color='red', alpha=0.7)
        axes[0, 0].bar(x + width/2, reconstructed_means, width,
                      label='伪装重建信号', color='green', alpha=0.7)

        axes[0, 0].set_title('信号统计特性对比', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('统计特性')
        axes[0, 0].set_ylabel('平均值')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(stats_names)
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 特征空间距离
        feature_distances = np.linalg.norm(original_features - fake_features, axis=1)

        axes[0, 1].hist(feature_distances, bins=20, alpha=0.7, color='purple', edgecolor='black')
        axes[0, 1].set_title('特征空间距离分布', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('欧氏距离')
        axes[0, 1].set_ylabel('频次')
        axes[0, 1].grid(True, alpha=0.3)

        # 3. 分类器预测对比
        original_pred = self.classifier.predict(original_features, verbose=0)
        fake_pred = self.classifier.predict(fake_features, verbose=0)

        axes[1, 0].scatter(original_pred, fake_pred, alpha=0.7, s=50)
        axes[1, 0].plot([0, 1], [0, 1], 'r--', linewidth=2, label='y=x')
        axes[1, 0].set_title('分类器预测对比', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('原始信号癫痫概率')
        axes[1, 0].set_ylabel('伪装信号癫痫概率')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 4. 伪装效果统计
        masking_effectiveness = 1 - fake_pred.flatten()

        axes[1, 1].hist(masking_effectiveness, bins=20, alpha=0.7, color='green', edgecolor='black')
        axes[1, 1].axvline(np.mean(masking_effectiveness), color='red', linestyle='--',
                          linewidth=2, label=f'平均效果: {np.mean(masking_effectiveness):.3f}')
        axes[1, 1].set_title('病灶伪装效果分布', fontsize=14, fontweight='bold')
        axes[1, 1].set_xlabel('伪装效果 (1-癫痫概率)')
        axes[1, 1].set_ylabel('频次')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('癫痫信号vs伪装信号统计对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("统计特性对比完成!")


def main():
    """
    主函数
    """
    print("=" * 80)
    print("EEG信号重建与对比可视化")
    print("=" * 80)
    
    # 初始化重建器
    reconstructor = EEGSignalReconstructor()
    
    # 加载模型和数据
    if not reconstructor.load_models_and_data():
        print("❌ 模型和数据加载失败")
        return
    
    # 加载原始EEG信号
    original_signals, original_features = reconstructor.load_original_eeg_signals(num_samples=5)

    # 创建信号重建对比
    print("\n创建信号重建对比...")
    comparison_results = reconstructor.create_signal_reconstruction_comparison(
        original_signals, original_features
    )

    print("\n" + "=" * 80)
    print("EEG信号重建与对比完成!")
    print("=" * 80)
    print("主要发现:")
    print(f"- 原始癫痫信号平均预测概率: {np.mean(comparison_results['original_pred']):.3f}")
    print(f"- 伪装重建信号平均预测概率: {np.mean(comparison_results['fake_pred']):.3f}")
    print(f"- 平均伪装效果: {np.mean(1 - comparison_results['fake_pred']):.3f}")
    print(f"- 信号重建成功: {len(comparison_results['reconstructed_signals'])} 个样本")

    # 计算信号相似性
    signal_similarities = []
    for i in range(len(original_signals)):
        correlation = np.corrcoef(original_signals[i], comparison_results['reconstructed_signals'][i])[0, 1]
        signal_similarities.append(correlation)

    print(f"- 平均信号相关性: {np.mean(signal_similarities):.3f}")
    print(f"- 特征空间平均距离: {np.mean(np.linalg.norm(comparison_results['original_features'] - comparison_results['fake_features'], axis=1)):.3f}")

    print("\n生成的可视化文件:")
    print("- 癫痫信号vs伪装重建信号对比.png")
    print("- 癫痫信号vs伪装信号频谱对比.png")
    print("- 癫痫信号vs伪装信号统计对比.png")

    return reconstructor, comparison_results


if __name__ == "__main__":
    reconstructor, results = main()
