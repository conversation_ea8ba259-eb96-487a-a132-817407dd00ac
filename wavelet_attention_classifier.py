#!/usr/bin/env python3
"""
小波分解+注意力+特征拼接分类器
Wavelet Decomposition + Attention + Feature Fusion Classifier

架构设计:
1. 小波分解 - 提取多尺度时频特征
2. 注意力机制 - 自适应特征权重
3. 特征拼接 - 融合多层次信息
4. 深度分类 - 高精度二分类

目标: 达到95%+准确率区分控制组vs癫痫组
处理对象: 单个最显著发病EEG通道 (FC5)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
import pywt
import warnings
import os
import gzip
import json
from pathlib import Path
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class WaveletFeatureExtractor:
    """
    小波特征提取器
    """
    
    def __init__(self, wavelet='db4', levels=5):
        self.wavelet = wavelet
        self.levels = levels
        
        print(f"小波特征提取器初始化:")
        print(f"- 小波基: {wavelet}")
        print(f"- 分解层数: {levels}")
    
    def extract_wavelet_features(self, signals):
        """
        提取小波分解特征
        """
        print("提取小波分解特征...")
        
        all_features = []
        
        for i, signal in enumerate(signals):
            # 小波分解
            coeffs = pywt.wavedec(signal, self.wavelet, level=self.levels)
            
            # 提取各层特征
            features = []
            
            # 近似系数 (低频成分)
            cA = coeffs[0]
            features.extend([
                np.mean(cA), np.std(cA), np.var(cA),
                np.max(cA), np.min(cA),
                np.percentile(cA, 25), np.percentile(cA, 75),
                self.calculate_energy(cA),
                self.calculate_entropy(cA)
            ])
            
            # 细节系数 (高频成分)
            for j, cD in enumerate(coeffs[1:], 1):
                features.extend([
                    np.mean(cD), np.std(cD), np.var(cD),
                    np.max(cD), np.min(cD),
                    np.percentile(cD, 25), np.percentile(cD, 75),
                    self.calculate_energy(cD),
                    self.calculate_entropy(cD),
                    self.calculate_zero_crossing_rate(cD)
                ])
            
            # 频带能量比
            energies = [self.calculate_energy(c) for c in coeffs]
            total_energy = sum(energies)
            energy_ratios = [e/total_energy for e in energies]
            features.extend(energy_ratios)
            
            # 相对小波能量
            features.extend([
                energies[0] / energies[1] if energies[1] > 0 else 0,  # 低频/高频比
                max(energies[1:]) / energies[0] if energies[0] > 0 else 0  # 最大细节/近似比
            ])
            
            all_features.append(features)
            
            if (i + 1) % 1000 == 0:
                print(f"  已处理 {i + 1}/{len(signals)} 个信号")
        
        features_array = np.array(all_features)
        print(f"小波特征提取完成: {features_array.shape}")
        
        return features_array
    
    def calculate_energy(self, coeffs):
        """计算能量"""
        return np.sum(coeffs ** 2)
    
    def calculate_entropy(self, coeffs):
        """计算熵"""
        # 归一化
        coeffs_norm = np.abs(coeffs)
        coeffs_norm = coeffs_norm / (np.sum(coeffs_norm) + 1e-12)
        
        # 计算熵
        entropy = -np.sum(coeffs_norm * np.log2(coeffs_norm + 1e-12))
        return entropy
    
    def calculate_zero_crossing_rate(self, coeffs):
        """计算过零率"""
        return np.sum(np.diff(np.sign(coeffs)) != 0) / len(coeffs)


class AttentionFeatureFusion(layers.Layer):
    """
    注意力特征融合层 (修复维度问题)
    """

    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)

    def build(self, input_shape):
        # 注意力权重矩阵
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),  # 保持相同维度
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),  # 保持相同维度
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)

    def call(self, inputs):
        # 计算注意力权重
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)

        # 加权特征 (现在维度匹配)
        weighted_features = inputs * attention_weights

        return weighted_features

    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class WaveletAttentionClassifier:
    """
    小波分解+注意力+特征拼接分类器
    """
    
    def __init__(self, segment_length=512, sampling_rate=128):
        self.segment_length = segment_length
        self.sampling_rate = sampling_rate
        
        # 组件
        self.wavelet_extractor = WaveletFeatureExtractor(wavelet='db4', levels=5)
        self.model = None
        self.scaler = None
        self.history = None
        
        # 数据
        self.control_data = None
        self.epilepsy_data = None
        self.wavelet_features = None
        
        print(f"小波注意力分类器初始化:")
        print(f"- 处理通道: FC5 (最显著发病通道)")
        print(f"- 分段长度: {segment_length} 样本点")
        print(f"- 目标准确率: 95%+")
    
    def load_fc5_channel_data(self):
        """
        加载FC5通道数据 (最显著发病通道)
        """
        print("加载FC5通道数据...")
        
        # 加载Nigeria数据集元数据
        nigeria_metadata_path = "1252141/metadata_nigeria.csv"
        if not os.path.exists(nigeria_metadata_path):
            print("❌ 未找到Nigeria元数据文件")
            return False
        
        metadata = pd.read_csv(nigeria_metadata_path)
        
        # 分离控制组和癫痫组
        control_files = metadata[metadata['Group'] == 'control']['csv.file'].tolist()
        epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        
        print(f"数据集统计:")
        print(f"- 控制组文件数: {len(control_files)}")
        print(f"- 癫痫组文件数: {len(epilepsy_files)}")
        print(f"- 目标通道: FC5")
        
        # 加载数据
        control_segments = self.load_channel_segments(control_files, 'FC5', "1252141/EEGs_Nigeria")
        epilepsy_segments = self.load_channel_segments(epilepsy_files, 'FC5', "1252141/EEGs_Nigeria")
        
        if len(control_segments) == 0 or len(epilepsy_segments) == 0:
            print("❌ 数据加载失败")
            return False
        
        # 标准化原始信号
        all_segments = np.vstack([control_segments, epilepsy_segments])
        self.scaler = RobustScaler()
        all_segments_scaled = self.scaler.fit_transform(all_segments.reshape(-1, 1)).reshape(all_segments.shape)
        
        # 分离标准化后的数据
        self.control_data = all_segments_scaled[:len(control_segments)]
        self.epilepsy_data = all_segments_scaled[len(control_segments):]
        
        print(f"FC5通道数据加载完成:")
        print(f"- 控制组段数: {len(self.control_data)}")
        print(f"- 癫痫组段数: {len(self.epilepsy_data)}")
        print(f"- 数据范围: [{all_segments_scaled.min():.3f}, {all_segments_scaled.max():.3f}]")
        
        return True
    
    def load_channel_segments(self, file_list, channel, data_dir):
        """
        加载指定通道的数据段
        """
        all_segments = []
        
        for i, filename in enumerate(file_list[:40]):  # 增加文件数量以获得更多数据
            file_path = os.path.join(data_dir, filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                    
                    if channel in df.columns:
                        channel_data = df[channel].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values
                        
                        # 预处理
                        channel_data = self.preprocess_signal(channel_data)
                        
                        # 创建段
                        segments = self.create_segments(channel_data)
                        all_segments.extend(segments)
                        
                        if (i + 1) % 10 == 0:
                            print(f"  已处理 {i + 1}/{min(40, len(file_list))} 个文件")
                
                except Exception as e:
                    print(f"  跳过文件 {filename}: {e}")
        
        return np.array(all_segments) if all_segments else np.array([])
    
    def preprocess_signal(self, signal):
        """
        预处理单个信号
        """
        from scipy import signal as scipy_signal
        
        # 去除异常值
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        signal_clean = np.clip(signal, mean_val - 3*std_val, mean_val + 3*std_val)
        
        # 去趋势
        x = np.arange(len(signal_clean))
        coeffs = np.polyfit(x, signal_clean, 1)
        trend = np.polyval(coeffs, x)
        signal_detrend = signal_clean - trend
        
        # 带通滤波 (0.5-60Hz)
        nyquist = self.sampling_rate / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist
        
        if low_freq < 1 and high_freq < 1:
            b, a = scipy_signal.butter(4, [low_freq, high_freq], btype='band')
            signal_filtered = scipy_signal.filtfilt(b, a, signal_detrend)
        else:
            signal_filtered = signal_detrend
        
        # 标准化
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / (np.std(signal_filtered) + 1e-8)
        
        return signal_normalized
    
    def create_segments(self, signal):
        """
        创建数据段
        """
        segments = []
        step_size = self.segment_length // 4  # 75%重叠
        
        for i in range(0, len(signal) - self.segment_length + 1, step_size):
            segment = signal[i:i + self.segment_length]
            
            if np.std(segment) > 0.01:  # 过滤低变异段
                segments.append(segment)
        
        return segments

    def extract_all_features(self):
        """
        提取所有特征 (小波特征 + 时域特征 + 频域特征)
        """
        print("提取所有特征...")

        # 合并所有数据
        all_signals = np.vstack([self.control_data, self.epilepsy_data])
        labels = np.hstack([np.zeros(len(self.control_data)), np.ones(len(self.epilepsy_data))])

        print(f"总数据量: {len(all_signals)} (控制组: {len(self.control_data)}, 癫痫组: {len(self.epilepsy_data)})")

        # 1. 提取小波特征
        wavelet_features = self.wavelet_extractor.extract_wavelet_features(all_signals)

        # 2. 提取时域特征
        time_features = self.extract_time_domain_features(all_signals)

        # 3. 提取频域特征
        freq_features = self.extract_frequency_domain_features(all_signals)

        # 4. 拼接所有特征 (去掉非线性特征)
        all_features = np.hstack([wavelet_features, time_features, freq_features])

        print(f"特征拼接完成:")
        print(f"- 小波特征: {wavelet_features.shape[1]} 维")
        print(f"- 时域特征: {time_features.shape[1]} 维")
        print(f"- 频域特征: {freq_features.shape[1]} 维")
        print(f"- 总特征维度: {all_features.shape[1]} 维")

        # 特征标准化
        feature_scaler = StandardScaler()
        all_features_scaled = feature_scaler.fit_transform(all_features)

        self.wavelet_features = all_features_scaled
        self.feature_scaler = feature_scaler

        return all_features_scaled, labels

    def extract_time_domain_features(self, signals):
        """
        提取时域特征
        """
        print("提取时域特征...")

        time_features = []

        for signal in signals:
            features = [
                np.mean(signal),           # 均值
                np.std(signal),            # 标准差
                np.var(signal),            # 方差
                np.max(signal),            # 最大值
                np.min(signal),            # 最小值
                np.ptp(signal),            # 峰峰值
                np.percentile(signal, 25), # 25%分位数
                np.percentile(signal, 75), # 75%分位数
                np.median(signal),         # 中位数
                np.mean(np.abs(signal)),   # 平均绝对值
                np.sqrt(np.mean(signal**2)), # RMS
                len(signal[signal > 0]) / len(signal), # 正值比例
                np.sum(np.diff(signal) > 0) / len(signal), # 上升趋势比例
            ]

            # 峰度和偏度
            from scipy import stats
            features.extend([
                stats.kurtosis(signal),    # 峰度
                stats.skew(signal)         # 偏度
            ])

            time_features.append(features)

        return np.array(time_features)

    def extract_frequency_domain_features(self, signals):
        """
        提取频域特征
        """
        print("提取频域特征...")

        freq_features = []

        for signal in signals:
            # FFT
            fft = np.fft.fft(signal)
            freqs = np.fft.fftfreq(len(signal), 1/self.sampling_rate)

            # 功率谱密度
            psd = np.abs(fft) ** 2

            # 频带划分
            delta_band = (0.5, 4)
            theta_band = (4, 8)
            alpha_band = (8, 13)
            beta_band = (13, 30)
            gamma_band = (30, 60)

            bands = [delta_band, theta_band, alpha_band, beta_band, gamma_band]
            band_powers = []

            for low, high in bands:
                band_mask = (freqs >= low) & (freqs <= high)
                band_power = np.sum(psd[band_mask])
                band_powers.append(band_power)

            total_power = np.sum(band_powers)

            features = [
                np.mean(psd),              # 平均功率
                np.std(psd),               # 功率标准差
                np.max(psd),               # 最大功率
                freqs[np.argmax(psd)],     # 主频率
                total_power,               # 总功率
            ]

            # 相对频带功率
            if total_power > 0:
                features.extend([p/total_power for p in band_powers])
            else:
                features.extend([0] * len(band_powers))

            # 频带功率比
            if band_powers[1] > 0:  # theta > 0
                features.append(band_powers[0] / band_powers[1])  # delta/theta
            else:
                features.append(0)

            if band_powers[0] > 0:  # delta > 0
                features.append(band_powers[2] / band_powers[0])  # alpha/delta
            else:
                features.append(0)

            freq_features.append(features)

        return np.array(freq_features)

    def build_wavelet_attention_model(self, input_dim):
        """
        构建小波注意力分类模型 (简化版 - 去掉非线性特征)
        """
        print("构建小波注意力分类模型...")

        # 输入层
        input_layer = keras.Input(shape=(input_dim,), name='feature_input')

        # 特征分组处理
        # 特征按顺序排列: [小波特征, 时域特征, 频域特征]
        wavelet_dim = 67  # 根据小波分解层数计算得出
        time_dim = 15
        freq_dim = 12

        # 分离不同类型的特征
        wavelet_features = layers.Lambda(lambda x: x[:, :wavelet_dim], name='wavelet_slice')(input_layer)
        time_features = layers.Lambda(lambda x: x[:, wavelet_dim:wavelet_dim+time_dim], name='time_slice')(input_layer)
        freq_features = layers.Lambda(lambda x: x[:, wavelet_dim+time_dim:wavelet_dim+time_dim+freq_dim], name='freq_slice')(input_layer)

        # 各特征分支处理
        # 小波特征分支 (最重要的特征)
        wavelet_branch = layers.Dense(128, activation='relu')(wavelet_features)
        wavelet_branch = layers.BatchNormalization()(wavelet_branch)
        wavelet_branch = layers.Dropout(0.3)(wavelet_branch)
        wavelet_branch = AttentionFeatureFusion()(wavelet_branch)

        # 时域特征分支
        time_branch = layers.Dense(64, activation='relu')(time_features)
        time_branch = layers.BatchNormalization()(time_branch)
        time_branch = layers.Dropout(0.2)(time_branch)
        time_branch = AttentionFeatureFusion()(time_branch)

        # 频域特征分支
        freq_branch = layers.Dense(64, activation='relu')(freq_features)
        freq_branch = layers.BatchNormalization()(freq_branch)
        freq_branch = layers.Dropout(0.2)(freq_branch)
        freq_branch = AttentionFeatureFusion()(freq_branch)

        # 特征融合 (只有三个分支)
        merged_features = layers.Concatenate()([wavelet_branch, time_branch, freq_branch])

        # 全局注意力
        global_attention = AttentionFeatureFusion()(merged_features)  # 128+64+64=256

        # 深度分类层
        dense1 = layers.Dense(256, activation='relu')(global_attention)
        dense1 = layers.BatchNormalization()(dense1)
        dense1 = layers.Dropout(0.4)(dense1)

        dense2 = layers.Dense(128, activation='relu')(dense1)
        dense2 = layers.BatchNormalization()(dense2)
        dense2 = layers.Dropout(0.3)(dense2)

        dense3 = layers.Dense(64, activation='relu')(dense2)
        dense3 = layers.BatchNormalization()(dense3)
        dense3 = layers.Dropout(0.2)(dense3)

        # 输出层
        output = layers.Dense(1, activation='sigmoid', name='classification_output')(dense3)

        # 构建模型
        model = keras.Model(input_layer, output, name='wavelet_attention_classifier')

        # 编译模型
        optimizer = keras.optimizers.AdamW(
            learning_rate=0.001,
            weight_decay=0.01,
            beta_1=0.9,
            beta_2=0.999
        )

        model.compile(
            optimizer=optimizer,
            loss='binary_crossentropy',
            metrics=['accuracy', 'precision', 'recall', 'auc']
        )

        self.model = model

        print("小波注意力分类模型构建完成:")
        print(f"- 总参数: {model.count_params():,}")
        print(f"- 特征维度: {input_dim}")
        print("- 架构: 小波分解 + 时频域特征 + 注意力机制")

        return model


def main():
    """
    主函数 - 小波注意力分类器
    """
    print("=" * 80)
    print("小波分解+注意力+特征拼接分类器")
    print("=" * 80)
    
    # 初始化分类器
    classifier = WaveletAttentionClassifier()
    
    # 步骤1: 加载FC5通道数据
    print("\n步骤1: 加载FC5通道数据")
    if not classifier.load_fc5_channel_data():
        print("❌ 数据加载失败，退出程序")
        return
    
    # 步骤2: 提取所有特征
    print("\n步骤2: 提取小波+时频+非线性特征")
    features, labels = classifier.extract_all_features()

    # 步骤3: 构建模型
    print("\n步骤3: 构建小波注意力分类模型")
    model = classifier.build_wavelet_attention_model(features.shape[1])

    # 步骤4: 训练模型
    print("\n步骤4: 训练模型达到95%+准确率")
    history, test_results = train_high_accuracy_model(classifier, features, labels)

    # 步骤5: 详细评估
    print("\n步骤5: 详细性能评估")
    evaluate_model_performance(classifier, features, labels, test_results)

    # 步骤6: 可视化结果
    print("\n步骤6: 创建可视化分析")
    create_classification_visualizations(classifier, history, test_results)

    # 步骤7: 保存模型
    print("\n步骤7: 保存模型和结果")
    save_classifier_results(classifier, features, labels)

    print("\n" + "=" * 80)
    print("小波注意力分类器完成!")
    print("=" * 80)

    return classifier


def train_high_accuracy_model(classifier, features, labels):
    """
    训练高精度模型
    """
    print("训练高精度分类模型...")

    # 分层划分数据集
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels, test_size=0.2, random_state=42, stratify=labels
    )

    X_train, X_val, y_train, y_val = train_test_split(
        X_train, y_train, test_size=0.2, random_state=42, stratify=y_train
    )

    print(f"数据划分:")
    print(f"- 训练集: {len(X_train)} (控制组: {np.sum(y_train==0)}, 癫痫组: {np.sum(y_train==1)})")
    print(f"- 验证集: {len(X_val)} (控制组: {np.sum(y_val==0)}, 癫痫组: {np.sum(y_val==1)})")
    print(f"- 测试集: {len(X_test)} (控制组: {np.sum(y_test==0)}, 癫痫组: {np.sum(y_test==1)})")

    # 高级回调函数
    callbacks = [
        keras.callbacks.EarlyStopping(
            monitor='val_accuracy',
            patience=25,
            restore_best_weights=True,
            verbose=1,
            mode='max'
        ),
        keras.callbacks.ReduceLROnPlateau(
            monitor='val_accuracy',
            factor=0.5,
            patience=12,
            min_lr=1e-7,
            verbose=1,
            mode='max'
        ),
        keras.callbacks.ModelCheckpoint(
            'best_wavelet_classifier.keras',
            monitor='val_accuracy',
            save_best_only=True,
            verbose=1,
            mode='max'
        )
    ]

    # 训练模型
    print("开始训练...")
    history = classifier.model.fit(
        X_train, y_train,
        validation_data=(X_val, y_val),
        epochs=200,
        batch_size=64,
        callbacks=callbacks,
        verbose=1
    )

    classifier.history = history

    # 测试集评估
    test_loss, test_acc, test_prec, test_rec, test_auc = classifier.model.evaluate(X_test, y_test, verbose=0)

    print(f"\n最终测试集性能:")
    print(f"- 测试准确率: {test_acc:.4f} ({test_acc*100:.2f}%)")
    print(f"- 测试精确率: {test_prec:.4f}")
    print(f"- 测试召回率: {test_rec:.4f}")
    print(f"- 测试AUC: {test_auc:.4f}")

    if test_acc >= 0.95:
        print("🎉 成功达到95%+准确率目标!")
    else:
        print(f"⚠️  当前准确率 {test_acc*100:.2f}%，距离95%目标还差 {(0.95-test_acc)*100:.2f}%")

    test_results = {
        'X_test': X_test, 'y_test': y_test,
        'test_acc': test_acc, 'test_prec': test_prec,
        'test_rec': test_rec, 'test_auc': test_auc
    }

    return history, test_results


def evaluate_model_performance(classifier, features, labels, test_results):
    """
    详细评估模型性能
    """
    print("详细性能评估...")

    X_test, y_test = test_results['X_test'], test_results['y_test']

    # 预测
    y_pred_proba = classifier.model.predict(X_test, verbose=0)
    y_pred = (y_pred_proba > 0.5).astype(int).flatten()

    # 分类报告
    print("\n详细分类报告:")
    print(classification_report(y_test, y_pred, target_names=['控制组', '癫痫组']))

    # 混淆矩阵
    cm = confusion_matrix(y_test, y_pred)
    print(f"\n混淆矩阵:")
    print(f"真实\\预测  控制组  癫痫组")
    print(f"控制组    {cm[0,0]:6d}  {cm[0,1]:6d}")
    print(f"癫痫组    {cm[1,0]:6d}  {cm[1,1]:6d}")

    # 计算各种指标
    tn, fp, fn, tp = cm.ravel()

    sensitivity = tp / (tp + fn)  # 敏感性 (召回率)
    specificity = tn / (tn + fp)  # 特异性
    ppv = tp / (tp + fp)          # 阳性预测值 (精确率)
    npv = tn / (tn + fn)          # 阴性预测值
    f1 = 2 * (ppv * sensitivity) / (ppv + sensitivity)  # F1分数

    print(f"\n详细性能指标:")
    print(f"- 敏感性 (Sensitivity): {sensitivity:.4f} ({sensitivity*100:.2f}%)")
    print(f"- 特异性 (Specificity): {specificity:.4f} ({specificity*100:.2f}%)")
    print(f"- 阳性预测值 (PPV): {ppv:.4f} ({ppv*100:.2f}%)")
    print(f"- 阴性预测值 (NPV): {npv:.4f} ({npv*100:.2f}%)")
    print(f"- F1分数: {f1:.4f}")
    print(f"- AUC: {test_results['test_auc']:.4f}")

    # 5折交叉验证
    print("\n5折交叉验证:")
    cv_scores = []
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

    for fold, (train_idx, val_idx) in enumerate(skf.split(features, labels)):
        X_train_cv, X_val_cv = features[train_idx], features[val_idx]
        y_train_cv, y_val_cv = labels[train_idx], labels[val_idx]

        # 创建新模型
        cv_model = classifier.build_wavelet_attention_model(features.shape[1])

        # 训练
        cv_model.fit(X_train_cv, y_train_cv,
                    validation_data=(X_val_cv, y_val_cv),
                    epochs=50, batch_size=64, verbose=0)

        # 评估
        _, cv_acc, _, _, _ = cv_model.evaluate(X_val_cv, y_val_cv, verbose=0)
        cv_scores.append(cv_acc)

        print(f"  Fold {fold+1}: {cv_acc:.4f} ({cv_acc*100:.2f}%)")

    print(f"交叉验证平均准确率: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")

    return {
        'sensitivity': sensitivity, 'specificity': specificity,
        'ppv': ppv, 'npv': npv, 'f1': f1,
        'cv_scores': cv_scores, 'cv_mean': np.mean(cv_scores), 'cv_std': np.std(cv_scores)
    }


def create_classification_visualizations(classifier, history, test_results):
    """
    创建分类可视化
    """
    print("创建分类可视化...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 训练历史
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))

    history_dict = history.history

    # 准确率
    axes[0, 0].plot(history_dict['accuracy'], 'b-', label='训练准确率', linewidth=2)
    axes[0, 0].plot(history_dict['val_accuracy'], 'r-', label='验证准确率', linewidth=2)
    axes[0, 0].axhline(y=0.95, color='g', linestyle='--', label='95%目标线', linewidth=2)
    axes[0, 0].set_title('小波注意力分类器准确率', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('训练轮数')
    axes[0, 0].set_ylabel('准确率')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 损失
    axes[0, 1].plot(history_dict['loss'], 'b-', label='训练损失', linewidth=2)
    axes[0, 1].plot(history_dict['val_loss'], 'r-', label='验证损失', linewidth=2)
    axes[0, 1].set_title('损失曲线', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('训练轮数')
    axes[0, 1].set_ylabel('损失值')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # AUC
    axes[1, 0].plot(history_dict['auc'], 'g-', label='训练AUC', linewidth=2)
    axes[1, 0].plot(history_dict['val_auc'], 'orange', label='验证AUC', linewidth=2)
    axes[1, 0].set_title('AUC曲线', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('训练轮数')
    axes[1, 0].set_ylabel('AUC值')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 精确率和召回率
    axes[1, 1].plot(history_dict['precision'], 'purple', label='训练精确率', linewidth=2)
    axes[1, 1].plot(history_dict['val_precision'], 'brown', label='验证精确率', linewidth=2)
    axes[1, 1].plot(history_dict['recall'], 'pink', label='训练召回率', linewidth=2)
    axes[1, 1].plot(history_dict['val_recall'], 'gray', label='验证召回率', linewidth=2)
    axes[1, 1].set_title('精确率和召回率', fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel('训练轮数')
    axes[1, 1].set_ylabel('分数')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('小波注意力分类器训练历史.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. ROC曲线和混淆矩阵
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))

    X_test, y_test = test_results['X_test'], test_results['y_test']
    y_pred_proba = classifier.model.predict(X_test, verbose=0)
    y_pred = (y_pred_proba > 0.5).astype(int).flatten()

    # ROC曲线
    fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
    auc_score = roc_auc_score(y_test, y_pred_proba)

    axes[0].plot(fpr, tpr, 'b-', linewidth=2, label=f'ROC曲线 (AUC = {auc_score:.3f})')
    axes[0].plot([0, 1], [0, 1], 'r--', linewidth=2, label='随机分类器')
    axes[0].set_xlabel('假阳性率 (1-特异性)')
    axes[0].set_ylabel('真阳性率 (敏感性)')
    axes[0].set_title('ROC曲线', fontsize=14, fontweight='bold')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 混淆矩阵热图
    cm = confusion_matrix(y_test, y_pred)
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                xticklabels=['控制组', '癫痫组'],
                yticklabels=['控制组', '癫痫组'],
                ax=axes[1])
    axes[1].set_title('混淆矩阵', fontsize=14, fontweight='bold')
    axes[1].set_xlabel('预测标签')
    axes[1].set_ylabel('真实标签')

    plt.tight_layout()
    plt.savefig('小波注意力分类器性能评估.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("分类可视化创建完成!")


def save_classifier_results(classifier, features, labels):
    """
    保存分类器结果
    """
    print("保存分类器结果...")

    # 创建结果目录
    os.makedirs('wavelet_classifier_results', exist_ok=True)

    # 保存模型
    classifier.model.save('wavelet_classifier_results/wavelet_attention_classifier.keras')

    # 保存标准化器
    import joblib
    joblib.dump(classifier.scaler, 'wavelet_classifier_results/signal_scaler.pkl')
    joblib.dump(classifier.feature_scaler, 'wavelet_classifier_results/feature_scaler.pkl')

    # 保存特征和标签
    np.save('wavelet_classifier_results/extracted_features.npy', features)
    np.save('wavelet_classifier_results/labels.npy', labels)

    # 保存配置
    config = {
        'model_type': 'wavelet_attention_classifier',
        'target_channel': 'FC5',
        'segment_length': classifier.segment_length,
        'sampling_rate': classifier.sampling_rate,
        'feature_dimensions': {
            'total': features.shape[1],
            'wavelet': 67,
            'time_domain': 15,
            'frequency_domain': 12
        },
        'model_architecture': 'wavelet_decomposition + attention + feature_fusion',
        'target_accuracy': 0.95
    }

    with open('wavelet_classifier_results/model_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)

    print("分类器结果保存完成!")


if __name__ == "__main__":
    classifier = main()
