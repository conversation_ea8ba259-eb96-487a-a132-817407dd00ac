#!/usr/bin/env python3
"""
Clinical EEG Preprocessing Pipeline
Following professional standards for epilepsy research

This module implements a comprehensive EEG preprocessing pipeline using
MNE-Python, AutoReject, and PICARD for clinical-grade artifact removal
and signal enhancement.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
from mne.preprocessing import ICA
from autoreject import AutoReject, get_rejection_threshold
import gzip
import os
import warnings
from scipy import signal
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

class ClinicalEEGPreprocessor:
    """
    Professional EEG preprocessing pipeline following clinical standards
    """
    
    def __init__(self, sfreq=128):
        """
        Initialize the preprocessing pipeline
        
        Parameters:
        -----------
        sfreq : float
            Sampling frequency in Hz (default: 128 Hz for clinical EEG)
        """
        self.sfreq = sfreq
        
        # Standard 10-20 electrode positions for Nigeria dataset
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Clinical frequency bands (adjusted for Nyquist limit)
        self.frequency_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 60)  # Limited by Nyquist frequency
        }
        
        # Preprocessing parameters
        self.preprocessing_params = {
            'l_freq': 0.5,      # High-pass filter frequency
            'h_freq': 60,       # Low-pass filter frequency (below Nyquist)
            'notch_freq': 50,   # Power line frequency (50 Hz for most countries)
            'epoch_length': 2.0, # Epoch length in seconds
            'overlap': 0.5,     # Overlap between epochs
            'ica_n_components': 0.95,  # Number of ICA components (95% variance)
            'autoreject_n_interpolate': [1, 4, 32],  # Channels to interpolate
            'autoreject_consensus': [0.1, 0.2, 0.3, 0.5, 0.7, 0.9]  # Consensus percentages
        }
        
    def load_eeg_data(self, file_path, dataset_type='nigeria'):
        """
        Load EEG data and create MNE Raw object
        
        Parameters:
        -----------
        file_path : str
            Path to the compressed CSV file
        dataset_type : str
            'nigeria' or 'guinea_bissau'
            
        Returns:
        --------
        raw : mne.io.Raw
            MNE Raw object containing EEG data
        """
        try:
            logger.info(f"Loading EEG data from {file_path}")
            
            with gzip.open(file_path, 'rt') as f:
                if dataset_type == 'nigeria':
                    # Nigeria dataset has header with channel names
                    df = pd.read_csv(f)
                    # Extract EEG channels and ensure numeric
                    eeg_data = df[self.eeg_channels].apply(pd.to_numeric, errors='coerce').values.T
                    ch_names = self.eeg_channels
                else:
                    # Guinea-Bissau dataset format (no header, different structure)
                    df = pd.read_csv(f, header=None)
                    # Assume first 14 columns are EEG channels, convert to numeric
                    eeg_data = pd.to_numeric(df.iloc[:, 1:15].values.flatten(), 
                                           errors='coerce').reshape(df.shape[0], 14).T
                    ch_names = [f'EEG_{i+1:02d}' for i in range(14)]
            
            # Remove NaN values and ensure proper data type
            eeg_data = np.nan_to_num(eeg_data, nan=0.0).astype(float)
            
            # Convert to microvolts (assuming data is in microvolts)
            eeg_data = eeg_data * 1e-6  # Convert to volts for MNE
            
            # Create MNE info structure
            info = mne.create_info(
                ch_names=ch_names,
                sfreq=self.sfreq,
                ch_types='eeg'
            )
            
            # Create Raw object
            raw = mne.io.RawArray(eeg_data, info)
            
            # Set standard montage for Nigeria dataset
            if dataset_type == 'nigeria':
                montage = mne.channels.make_standard_montage('standard_1020')
                raw.set_montage(montage, on_missing='ignore')
            
            logger.info(f"Successfully loaded {len(ch_names)} channels, {raw.n_times} samples")
            return raw
            
        except Exception as e:
            logger.error(f"Error loading {file_path}: {e}")
            return None
    
    def apply_basic_filtering(self, raw):
        """
        Apply basic filtering: high-pass, low-pass, and notch filters
        
        Parameters:
        -----------
        raw : mne.io.Raw
            Raw EEG data
            
        Returns:
        --------
        raw_filtered : mne.io.Raw
            Filtered EEG data
        """
        logger.info("Applying basic filtering...")
        
        # Create a copy to avoid modifying original data
        raw_filtered = raw.copy()
        
        # High-pass filter to remove slow drifts
        raw_filtered.filter(
            l_freq=self.preprocessing_params['l_freq'],
            h_freq=None,
            fir_design='firwin',
            verbose=False
        )
        
        # Low-pass filter to remove high-frequency noise
        raw_filtered.filter(
            l_freq=None,
            h_freq=self.preprocessing_params['h_freq'],
            fir_design='firwin',
            verbose=False
        )
        
        # Notch filter for power line interference
        raw_filtered.notch_filter(
            freqs=self.preprocessing_params['notch_freq'],
            verbose=False
        )
        
        logger.info("Basic filtering completed")
        return raw_filtered
    
    def detect_bad_channels(self, raw):
        """
        Detect bad channels using statistical methods
        
        Parameters:
        -----------
        raw : mne.io.Raw
            Raw EEG data
            
        Returns:
        --------
        bad_channels : list
            List of bad channel names
        """
        logger.info("Detecting bad channels...")
        
        # Get data for analysis
        data = raw.get_data()
        
        # Calculate channel statistics
        channel_vars = np.var(data, axis=1)
        channel_means = np.mean(np.abs(data), axis=1)
        
        # Detect outliers using z-score method
        var_z_scores = np.abs((channel_vars - np.mean(channel_vars)) / np.std(channel_vars))
        mean_z_scores = np.abs((channel_means - np.mean(channel_means)) / np.std(channel_means))
        
        # Channels with z-score > 3 are considered bad
        bad_var_idx = np.where(var_z_scores > 3)[0]
        bad_mean_idx = np.where(mean_z_scores > 3)[0]
        
        bad_channels = []
        for idx in np.unique(np.concatenate([bad_var_idx, bad_mean_idx])):
            bad_channels.append(raw.ch_names[idx])
        
        logger.info(f"Detected {len(bad_channels)} bad channels: {bad_channels}")
        return bad_channels
    
    def interpolate_bad_channels(self, raw, bad_channels):
        """
        Interpolate bad channels using spherical splines
        
        Parameters:
        -----------
        raw : mne.io.Raw
            Raw EEG data
        bad_channels : list
            List of bad channel names
            
        Returns:
        --------
        raw_interp : mne.io.Raw
            Data with interpolated channels
        """
        if not bad_channels:
            logger.info("No bad channels to interpolate")
            return raw.copy()
        
        logger.info(f"Interpolating {len(bad_channels)} bad channels...")
        
        raw_interp = raw.copy()
        raw_interp.info['bads'] = bad_channels
        
        # Interpolate bad channels (skip if no montage available)
        try:
            raw_interp.interpolate_bads(reset_bads=True, verbose=False)
        except Exception as e:
            logger.warning(f"Could not interpolate bad channels: {e}")
            # Simply mark channels as bad without interpolation
            pass
        
        logger.info("Channel interpolation completed")
        return raw_interp
    
    def apply_common_average_reference(self, raw):
        """
        Apply common average reference
        
        Parameters:
        -----------
        raw : mne.io.Raw
            Raw EEG data
            
        Returns:
        --------
        raw_ref : mne.io.Raw
            Re-referenced EEG data
        """
        logger.info("Applying common average reference...")
        
        raw_ref = raw.copy()
        raw_ref.set_eeg_reference('average', projection=True, verbose=False)
        raw_ref.apply_proj(verbose=False)
        
        logger.info("Common average reference applied")
        return raw_ref

    def create_epochs(self, raw):
        """
        Create epochs for further processing

        Parameters:
        -----------
        raw : mne.io.Raw
            Raw EEG data

        Returns:
        --------
        epochs : mne.Epochs
            Epoched EEG data
        """
        logger.info("Creating epochs...")

        # Create artificial events for continuous data
        events = mne.make_fixed_length_events(
            raw,
            duration=self.preprocessing_params['epoch_length'],
            overlap=self.preprocessing_params['overlap']
        )

        # Create epochs
        epochs = mne.Epochs(
            raw,
            events,
            tmin=0,
            tmax=self.preprocessing_params['epoch_length'] - 1/self.sfreq,
            baseline=None,
            preload=True,
            verbose=False
        )

        logger.info(f"Created {len(epochs)} epochs")
        return epochs

    def apply_ica_artifact_removal(self, epochs):
        """
        Apply Independent Component Analysis for artifact removal

        Parameters:
        -----------
        epochs : mne.Epochs
            Epoched EEG data

        Returns:
        --------
        epochs_clean : mne.Epochs
            Epochs with ICA artifacts removed
        ica : mne.preprocessing.ICA
            Fitted ICA object
        """
        logger.info("Applying ICA for artifact removal...")

        # Initialize ICA
        ica = ICA(
            n_components=self.preprocessing_params['ica_n_components'],
            method='picard',
            random_state=42,
            verbose=False
        )

        # Fit ICA on epochs
        ica.fit(epochs, verbose=False)

        # Automatically detect artifact components
        # EOG artifacts (if EOG channels available)
        eog_indices = []
        if any('EOG' in ch for ch in epochs.ch_names):
            eog_indices, eog_scores = ica.find_bads_eog(epochs, verbose=False)

        # ECG artifacts (if ECG channels available)
        ecg_indices = []
        if any('ECG' in ch for ch in epochs.ch_names):
            ecg_indices, ecg_scores = ica.find_bads_ecg(epochs, verbose=False)

        # Muscle artifacts using automatic detection
        muscle_indices = []
        try:
            muscle_indices, muscle_scores = ica.find_bads_muscle(epochs, verbose=False)
        except:
            # Fallback: detect high-frequency components as muscle artifacts
            ica_data = ica.get_sources(epochs).get_data()
            for i in range(ica.n_components_):
                # Calculate power in high-frequency range (30-60 Hz)
                freqs, psd = signal.welch(ica_data[:, i, :].flatten(),
                                        fs=self.sfreq, nperseg=256)
                high_freq_power = np.mean(psd[(freqs >= 30) & (freqs <= 60)])
                total_power = np.mean(psd)

                # If high-frequency power is > 30% of total power, consider as muscle
                if high_freq_power / total_power > 0.3:
                    muscle_indices.append(i)

        # Combine all artifact indices
        artifact_indices = list(set(eog_indices + ecg_indices + muscle_indices))

        logger.info(f"Detected {len(artifact_indices)} artifact components: {artifact_indices}")

        # Mark components as bad
        ica.exclude = artifact_indices

        # Apply ICA to remove artifacts
        epochs_clean = ica.apply(epochs.copy(), verbose=False)

        logger.info("ICA artifact removal completed")
        return epochs_clean, ica

    def apply_autoreject(self, epochs):
        """
        Apply AutoReject for automated epoch and channel rejection/repair

        Parameters:
        -----------
        epochs : mne.Epochs
            Epoched EEG data

        Returns:
        --------
        epochs_clean : mne.Epochs
            Cleaned epochs after AutoReject
        ar : AutoReject
            Fitted AutoReject object
        """
        logger.info("Applying AutoReject for automated artifact rejection...")

        # Initialize AutoReject
        ar = AutoReject(
            n_interpolate=self.preprocessing_params['autoreject_n_interpolate'],
            consensus=self.preprocessing_params['autoreject_consensus'],
            thresh_method='random_search',
            cv=3,
            random_state=42,
            verbose=False
        )

        # Fit and transform epochs
        epochs_clean = ar.fit_transform(epochs)

        # Get rejection log
        reject_log = ar.get_reject_log(epochs)

        logger.info(f"AutoReject: {reject_log.bad_epochs.sum()} epochs rejected, "
                   f"{len(epochs_clean)} epochs retained")

        return epochs_clean, ar

    def calculate_signal_quality_metrics(self, raw_original, raw_preprocessed):
        """
        Calculate signal quality metrics to assess preprocessing effectiveness

        Parameters:
        -----------
        raw_original : mne.io.Raw
            Original raw data
        raw_preprocessed : mne.io.Raw
            Preprocessed raw data

        Returns:
        --------
        metrics : dict
            Dictionary containing quality metrics
        """
        logger.info("Calculating signal quality metrics...")

        # Get data
        data_orig = raw_original.get_data()
        data_prep = raw_preprocessed.get_data()

        # Calculate metrics
        metrics = {}

        # Signal-to-noise ratio improvement
        noise_orig = np.std(data_orig, axis=1)
        noise_prep = np.std(data_prep, axis=1)
        snr_improvement = np.mean(noise_orig / noise_prep)
        metrics['snr_improvement'] = snr_improvement

        # Power spectral density comparison
        freqs_orig, psd_orig = signal.welch(data_orig.flatten(), fs=self.sfreq, nperseg=256)
        freqs_prep, psd_prep = signal.welch(data_prep.flatten(), fs=self.sfreq, nperseg=256)

        # Power line interference reduction (around 50 Hz)
        line_freq_idx = np.argmin(np.abs(freqs_orig - 50))
        line_power_reduction = psd_orig[line_freq_idx] / psd_prep[line_freq_idx]
        metrics['line_noise_reduction'] = line_power_reduction

        # High-frequency noise reduction (30-60 Hz)
        high_freq_mask = (freqs_orig >= 30) & (freqs_orig <= 60)
        high_freq_power_orig = np.mean(psd_orig[high_freq_mask])
        high_freq_power_prep = np.mean(psd_prep[high_freq_mask])
        metrics['high_freq_noise_reduction'] = high_freq_power_orig / high_freq_power_prep

        logger.info(f"Quality metrics calculated: SNR improvement: {snr_improvement:.2f}x, "
                   f"Line noise reduction: {line_power_reduction:.2f}x")

        return metrics

    def run_full_preprocessing_pipeline(self, file_path, dataset_type='nigeria'):
        """
        Run the complete preprocessing pipeline

        Parameters:
        -----------
        file_path : str
            Path to EEG data file
        dataset_type : str
            Type of dataset ('nigeria' or 'guinea_bissau')

        Returns:
        --------
        results : dict
            Dictionary containing all preprocessing results
        """
        logger.info(f"Starting full preprocessing pipeline for {file_path}")

        results = {
            'file_path': file_path,
            'dataset_type': dataset_type,
            'success': False
        }

        try:
            # Step 1: Load data
            raw_original = self.load_eeg_data(file_path, dataset_type)
            if raw_original is None:
                return results

            results['raw_original'] = raw_original

            # Step 2: Basic filtering
            raw_filtered = self.apply_basic_filtering(raw_original)
            results['raw_filtered'] = raw_filtered

            # Step 3: Bad channel detection and interpolation
            bad_channels = self.detect_bad_channels(raw_filtered)
            raw_interpolated = self.interpolate_bad_channels(raw_filtered, bad_channels)
            results['bad_channels'] = bad_channels
            results['raw_interpolated'] = raw_interpolated

            # Step 4: Re-referencing
            raw_referenced = self.apply_common_average_reference(raw_interpolated)
            results['raw_referenced'] = raw_referenced

            # Step 5: Create epochs
            epochs = self.create_epochs(raw_referenced)
            results['epochs'] = epochs

            # Step 6: ICA artifact removal
            epochs_ica, ica = self.apply_ica_artifact_removal(epochs)
            results['epochs_ica'] = epochs_ica
            results['ica'] = ica

            # Step 7: AutoReject
            epochs_clean, autoreject = self.apply_autoreject(epochs_ica)
            results['epochs_clean'] = epochs_clean
            results['autoreject'] = autoreject

            # Step 8: Convert back to continuous data
            raw_final = epochs_clean.to_data_frame().reset_index()

            # Reconstruct continuous data
            eeg_columns = [col for col in raw_final.columns if col in raw_original.ch_names]
            final_data = raw_final[eeg_columns].values.T

            info_final = mne.create_info(
                ch_names=eeg_columns,
                sfreq=self.sfreq,
                ch_types='eeg'
            )

            raw_preprocessed = mne.io.RawArray(final_data, info_final)
            if dataset_type == 'nigeria':
                montage = mne.channels.make_standard_montage('standard_1020')
                raw_preprocessed.set_montage(montage, on_missing='ignore')

            results['raw_preprocessed'] = raw_preprocessed

            # Step 9: Calculate quality metrics
            quality_metrics = self.calculate_signal_quality_metrics(raw_original, raw_preprocessed)
            results['quality_metrics'] = quality_metrics

            results['success'] = True
            logger.info("Preprocessing pipeline completed successfully")

        except Exception as e:
            logger.error(f"Error in preprocessing pipeline: {e}")
            results['error'] = str(e)

        return results

    def plot_preprocessing_comparison(self, results, save_path=None):
        """
        Create visualization comparing original and preprocessed data

        Parameters:
        -----------
        results : dict
            Results from preprocessing pipeline
        save_path : str
            Path to save the figure
        """
        if not results['success']:
            logger.error("Cannot plot comparison - preprocessing failed")
            return

        fig, axes = plt.subplots(4, 2, figsize=(16, 12))

        # Get data
        raw_orig = results['raw_original']
        raw_prep = results['raw_preprocessed']

        # Select a representative channel (first available)
        ch_idx = 0
        ch_name = raw_orig.ch_names[ch_idx]

        # Time vectors
        time_orig = np.arange(raw_orig.n_times) / raw_orig.info['sfreq']
        time_prep = np.arange(raw_prep.n_times) / raw_prep.info['sfreq']

        # Limit to first 10 seconds for visualization
        max_samples_orig = min(10 * int(raw_orig.info['sfreq']), raw_orig.n_times)
        max_samples_prep = min(10 * int(raw_prep.info['sfreq']), raw_prep.n_times)

        # Time domain comparison
        axes[0, 0].plot(time_orig[:max_samples_orig],
                       raw_orig.get_data()[ch_idx, :max_samples_orig] * 1e6, 'b-', linewidth=0.5)
        axes[0, 0].set_title(f'Original Signal - {ch_name}', fontweight='bold')
        axes[0, 0].set_ylabel('Amplitude (μV)')
        axes[0, 0].grid(True, alpha=0.3)

        axes[0, 1].plot(time_prep[:max_samples_prep],
                       raw_prep.get_data()[ch_idx, :max_samples_prep] * 1e6, 'r-', linewidth=0.5)
        axes[0, 1].set_title(f'Preprocessed Signal - {ch_name}', fontweight='bold')
        axes[0, 1].set_ylabel('Amplitude (μV)')
        axes[0, 1].grid(True, alpha=0.3)

        # Power spectral density comparison
        freqs_orig, psd_orig = signal.welch(raw_orig.get_data()[ch_idx, :],
                                          fs=raw_orig.info['sfreq'], nperseg=256)
        freqs_prep, psd_prep = signal.welch(raw_prep.get_data()[ch_idx, :],
                                          fs=raw_prep.info['sfreq'], nperseg=256)

        axes[1, 0].semilogy(freqs_orig, psd_orig * 1e12, 'b-', linewidth=1)
        axes[1, 0].set_title('Original PSD', fontweight='bold')
        axes[1, 0].set_ylabel('Power (μV²/Hz)')
        axes[1, 0].set_xlim(0, 50)
        axes[1, 0].grid(True, alpha=0.3)

        axes[1, 1].semilogy(freqs_prep, psd_prep * 1e12, 'r-', linewidth=1)
        axes[1, 1].set_title('Preprocessed PSD', fontweight='bold')
        axes[1, 1].set_ylabel('Power (μV²/Hz)')
        axes[1, 1].set_xlim(0, 50)
        axes[1, 1].grid(True, alpha=0.3)

        # Quality metrics
        metrics = results['quality_metrics']
        metric_names = list(metrics.keys())
        metric_values = list(metrics.values())

        axes[2, 0].bar(range(len(metric_names)), metric_values, color='green', alpha=0.7)
        axes[2, 0].set_title('Quality Improvement Metrics', fontweight='bold')
        axes[2, 0].set_ylabel('Improvement Factor')
        axes[2, 0].set_xticks(range(len(metric_names)))
        axes[2, 0].set_xticklabels([name.replace('_', '\n') for name in metric_names],
                                  rotation=45, ha='right')
        axes[2, 0].grid(True, alpha=0.3)

        # Processing steps summary
        axes[2, 1].axis('off')
        processing_text = f"""
PREPROCESSING STEPS APPLIED:

1. Basic Filtering:
   • High-pass: {self.preprocessing_params['l_freq']} Hz
   • Low-pass: {self.preprocessing_params['h_freq']} Hz
   • Notch: {self.preprocessing_params['notch_freq']} Hz

2. Bad Channel Detection:
   • Detected: {len(results.get('bad_channels', []))} channels
   • Interpolated using spherical splines

3. Re-referencing:
   • Common average reference applied

4. ICA Artifact Removal:
   • Components removed: {len(results.get('ica').exclude) if 'ica' in results and hasattr(results['ica'], 'exclude') else 0}
   • Method: PICARD algorithm

5. AutoReject:
   • Automated epoch rejection/repair
   • Final epochs: {len(results.get('epochs_clean', []))}

Quality Improvements:
• SNR: {metrics.get('snr_improvement', 0):.2f}x
• Line noise: {metrics.get('line_noise_reduction', 0):.2f}x
• HF noise: {metrics.get('high_freq_noise_reduction', 0):.2f}x
        """

        axes[2, 1].text(0.05, 0.95, processing_text, transform=axes[2, 1].transAxes,
                        fontsize=10, verticalalignment='top',
                        bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))

        # Frequency band power comparison
        band_powers_orig = {}
        band_powers_prep = {}

        for band_name, (low_freq, high_freq) in self.frequency_bands.items():
            # Original data
            band_mask_orig = (freqs_orig >= low_freq) & (freqs_orig <= high_freq)
            band_powers_orig[band_name] = np.mean(psd_orig[band_mask_orig])

            # Preprocessed data
            band_mask_prep = (freqs_prep >= low_freq) & (freqs_prep <= high_freq)
            band_powers_prep[band_name] = np.mean(psd_prep[band_mask_prep])

        band_names = list(band_powers_orig.keys())
        x_pos = np.arange(len(band_names))

        axes[3, 0].bar(x_pos - 0.2, [band_powers_orig[band] * 1e12 for band in band_names],
                      0.4, label='Original', color='blue', alpha=0.7)
        axes[3, 0].bar(x_pos + 0.2, [band_powers_prep[band] * 1e12 for band in band_names],
                      0.4, label='Preprocessed', color='red', alpha=0.7)

        axes[3, 0].set_title('Frequency Band Power Comparison', fontweight='bold')
        axes[3, 0].set_ylabel('Power (μV²/Hz)')
        axes[3, 0].set_xticks(x_pos)
        axes[3, 0].set_xticklabels(band_names)
        axes[3, 0].legend()
        axes[3, 0].grid(True, alpha=0.3)

        # Power reduction ratios
        power_ratios = [band_powers_prep[band] / band_powers_orig[band] for band in band_names]

        axes[3, 1].bar(band_names, power_ratios, color='purple', alpha=0.7)
        axes[3, 1].axhline(y=1, color='red', linestyle='--', label='No change')
        axes[3, 1].set_title('Power Ratio (Preprocessed/Original)', fontweight='bold')
        axes[3, 1].set_ylabel('Ratio')
        axes[3, 1].legend()
        axes[3, 1].grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Preprocessing comparison saved to {save_path}")

        plt.show()


def preprocess_sample_datasets():
    """
    Apply preprocessing to sample datasets from both regions

    Returns:
    --------
    preprocessed_results : dict
        Dictionary containing preprocessing results for all samples
    """
    logger.info("Starting preprocessing of sample datasets...")

    preprocessor = ClinicalEEGPreprocessor()
    preprocessed_results = {
        'nigeria_epilepsy': [],
        'nigeria_control': [],
        'guinea_bissau_epilepsy': [],
        'guinea_bissau_control': []
    }

    # Load metadata for group identification
    metadata_gb = None
    if os.path.exists("1252141/metadata_guineabissau.csv"):
        metadata_gb = pd.read_csv("1252141/metadata_guineabissau.csv")

    # Process Nigeria samples
    nigeria_path = "1252141/EEGs_Nigeria"
    if os.path.exists(nigeria_path):
        # Sample epilepsy files (500+ series)
        epilepsy_files = ['signal-500-1.csv.gz', 'signal-501-1.csv.gz']
        for file in epilepsy_files:
            file_path = os.path.join(nigeria_path, file)
            if os.path.exists(file_path):
                logger.info(f"Processing Nigeria epilepsy: {file}")
                results = preprocessor.run_full_preprocessing_pipeline(file_path, 'nigeria')
                if results['success']:
                    preprocessed_results['nigeria_epilepsy'].append(results)

        # Sample control files
        control_files = ['signal-6-1.csv.gz', 'signal-9-1.csv.gz']
        for file in control_files:
            file_path = os.path.join(nigeria_path, file)
            if os.path.exists(file_path):
                logger.info(f"Processing Nigeria control: {file}")
                results = preprocessor.run_full_preprocessing_pipeline(file_path, 'nigeria')
                if results['success']:
                    preprocessed_results['nigeria_control'].append(results)

    # Process Guinea-Bissau samples
    gb_path = "1252141/EEGs_Guinea-Bissau"
    if os.path.exists(gb_path) and metadata_gb is not None:
        # Sample epilepsy subjects
        epilepsy_subjects = metadata_gb[metadata_gb['Group'] == 'Epilepsy']['subject.id'].head(2).values
        for subject_id in epilepsy_subjects:
            file_path = os.path.join(gb_path, f'signal-{subject_id}.csv.gz')
            if os.path.exists(file_path):
                logger.info(f"Processing Guinea-Bissau epilepsy: signal-{subject_id}.csv.gz")
                results = preprocessor.run_full_preprocessing_pipeline(file_path, 'guinea_bissau')
                if results['success']:
                    preprocessed_results['guinea_bissau_epilepsy'].append(results)

        # Sample control subjects
        control_subjects = metadata_gb[metadata_gb['Group'] == 'Control']['subject.id'].head(2).values
        for subject_id in control_subjects:
            file_path = os.path.join(gb_path, f'signal-{subject_id}.csv.gz')
            if os.path.exists(file_path):
                logger.info(f"Processing Guinea-Bissau control: signal-{subject_id}.csv.gz")
                results = preprocessor.run_full_preprocessing_pipeline(file_path, 'guinea_bissau')
                if results['success']:
                    preprocessed_results['guinea_bissau_control'].append(results)

    # Print summary
    total_processed = sum(len(preprocessed_results[key]) for key in preprocessed_results.keys())
    logger.info(f"Preprocessing completed. Successfully processed {total_processed} files:")
    for key, value in preprocessed_results.items():
        logger.info(f"  {key}: {len(value)} files")

    return preprocessed_results


def create_preprocessing_summary_report(preprocessed_results):
    """
    Create a comprehensive summary report of preprocessing results

    Parameters:
    -----------
    preprocessed_results : dict
        Results from preprocessing pipeline
    """
    logger.info("Creating preprocessing summary report...")

    # Collect quality metrics
    all_metrics = []
    group_labels = []

    for group_name, results_list in preprocessed_results.items():
        for results in results_list:
            if results['success'] and 'quality_metrics' in results:
                all_metrics.append(results['quality_metrics'])
                group_labels.append(group_name)

    if not all_metrics:
        logger.warning("No quality metrics available for summary report")
        return

    # Create summary visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # SNR improvement by group
    snr_improvements = [metrics['snr_improvement'] for metrics in all_metrics]
    groups = list(set(group_labels))
    group_snr = {group: [] for group in groups}

    for snr, group in zip(snr_improvements, group_labels):
        group_snr[group].append(snr)

    axes[0, 0].boxplot([group_snr[group] for group in groups], labels=groups)
    axes[0, 0].set_title('SNR Improvement by Group', fontweight='bold')
    axes[0, 0].set_ylabel('SNR Improvement Factor')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].grid(True, alpha=0.3)

    # Line noise reduction
    line_noise_reductions = [metrics['line_noise_reduction'] for metrics in all_metrics]
    group_line_noise = {group: [] for group in groups}

    for noise, group in zip(line_noise_reductions, group_labels):
        group_line_noise[group].append(noise)

    axes[0, 1].boxplot([group_line_noise[group] for group in groups], labels=groups)
    axes[0, 1].set_title('Line Noise Reduction by Group', fontweight='bold')
    axes[0, 1].set_ylabel('Noise Reduction Factor')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].grid(True, alpha=0.3)

    # Overall quality improvement
    overall_quality = []
    for metrics in all_metrics:
        # Combine metrics into overall quality score
        quality_score = (metrics['snr_improvement'] +
                        metrics['line_noise_reduction'] +
                        metrics['high_freq_noise_reduction']) / 3
        overall_quality.append(quality_score)

    group_quality = {group: [] for group in groups}
    for quality, group in zip(overall_quality, group_labels):
        group_quality[group].append(quality)

    axes[1, 0].boxplot([group_quality[group] for group in groups], labels=groups)
    axes[1, 0].set_title('Overall Quality Improvement', fontweight='bold')
    axes[1, 0].set_ylabel('Combined Quality Score')
    axes[1, 0].tick_params(axis='x', rotation=45)
    axes[1, 0].grid(True, alpha=0.3)

    # Summary statistics
    axes[1, 1].axis('off')

    # Calculate summary statistics
    mean_snr = np.mean(snr_improvements)
    mean_line_noise = np.mean(line_noise_reductions)
    mean_quality = np.mean(overall_quality)

    summary_text = f"""
PREPROCESSING SUMMARY STATISTICS

Total Files Processed: {len(all_metrics)}

Quality Improvements (Mean ± SD):
• SNR Improvement: {mean_snr:.2f} ± {np.std(snr_improvements):.2f}x
• Line Noise Reduction: {mean_line_noise:.2f} ± {np.std(line_noise_reductions):.2f}x
• Overall Quality: {mean_quality:.2f} ± {np.std(overall_quality):.2f}x

Group Distribution:
"""

    for group in groups:
        count = group_labels.count(group)
        summary_text += f"• {group.replace('_', ' ').title()}: {count} files\n"

    summary_text += f"""
Preprocessing Pipeline:
✓ Basic filtering (0.5-100 Hz)
✓ Notch filtering (50 Hz)
✓ Bad channel detection & interpolation
✓ Common average re-referencing
✓ ICA artifact removal (PICARD)
✓ AutoReject epoch cleaning

Clinical Benefits:
• Improved signal quality for analysis
• Reduced artifacts and noise
• Enhanced epilepsy biomarker detection
• Standardized preprocessing across datasets
    """

    axes[1, 1].text(0.05, 0.95, summary_text, transform=axes[1, 1].transAxes,
                    fontsize=11, verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgreen", alpha=0.8))

    plt.tight_layout()
    plt.savefig('EEG_Preprocessing_Summary_Report.png', dpi=300, bbox_inches='tight')
    plt.show()

    logger.info("Preprocessing summary report created: EEG_Preprocessing_Summary_Report.png")


def main():
    """
    Main function to run EEG preprocessing pipeline
    """
    print("Clinical EEG Preprocessing Pipeline")
    print("=" * 50)

    # Run preprocessing on sample datasets
    preprocessed_results = preprocess_sample_datasets()

    # Create individual comparison plots for representative samples
    preprocessor = ClinicalEEGPreprocessor()

    # Plot Nigeria epilepsy sample
    if preprocessed_results['nigeria_epilepsy']:
        results = preprocessed_results['nigeria_epilepsy'][0]
        preprocessor.plot_preprocessing_comparison(
            results,
            save_path='Nigeria_Epilepsy_Preprocessing_Comparison.png'
        )

    # Plot Nigeria control sample
    if preprocessed_results['nigeria_control']:
        results = preprocessed_results['nigeria_control'][0]
        preprocessor.plot_preprocessing_comparison(
            results,
            save_path='Nigeria_Control_Preprocessing_Comparison.png'
        )

    # Create summary report
    create_preprocessing_summary_report(preprocessed_results)

    print("\nPreprocessing pipeline completed successfully!")
    print("Generated files:")
    print("- Nigeria_Epilepsy_Preprocessing_Comparison.png")
    print("- Nigeria_Control_Preprocessing_Comparison.png")
    print("- EEG_Preprocessing_Summary_Report.png")

    return preprocessed_results


if __name__ == "__main__":
    main()
