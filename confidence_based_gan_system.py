#!/usr/bin/env python3
"""
基于置信度的GAN病灶识别系统
Confidence-Based GAN Lesion Detection System

流程:
1. 加载训练好的小波注意力分类器
2. 计算所有样本的置信概率
3. 筛选高置信度样本 (置信度 > 阈值)
4. 用高置信度样本训练GAN病灶识别器
5. 让癫痫重建样本"伪装"成健康样本

目标: 通过置信度筛选提高GAN训练质量
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
import warnings
import os
import gzip
import json
from pathlib import Path
import joblib
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化以加载Lambda层
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层 (与原始模型相同)
class AttentionFeatureFusion(layers.Layer):
    """
    注意力特征融合层 (修复维度问题)
    """

    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)

    def build(self, input_shape):
        # 注意力权重矩阵
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),  # 保持相同维度
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),  # 保持相同维度
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)

    def call(self, inputs):
        # 计算注意力权重
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)

        # 加权特征 (现在维度匹配)
        weighted_features = inputs * attention_weights

        return weighted_features

    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config

class ConfidenceBasedSampleSelector:
    """
    基于置信度的样本选择器
    """
    
    def __init__(self, classifier_path, feature_scaler_path, signal_scaler_path):
        self.classifier = None
        self.feature_scaler = None
        self.signal_scaler = None
        
        # 加载模型和标准化器
        self.load_trained_models(classifier_path, feature_scaler_path, signal_scaler_path)
        
        print("置信度样本选择器初始化完成")
    
    def load_trained_models(self, classifier_path, feature_scaler_path, signal_scaler_path):
        """
        加载训练好的模型和标准化器
        """
        print("加载训练好的模型...")
        
        # 加载分类器 (提供自定义对象)
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(classifier_path, custom_objects=custom_objects)
        print(f"✅ 分类器加载完成: {classifier_path}")
        
        # 加载标准化器
        self.feature_scaler = joblib.load(feature_scaler_path)
        self.signal_scaler = joblib.load(signal_scaler_path)
        print(f"✅ 标准化器加载完成")
        
        # 显示模型信息
        print(f"分类器参数数量: {self.classifier.count_params():,}")
    
    def calculate_confidence_scores(self, features, labels):
        """
        计算所有样本的置信概率
        """
        print("计算样本置信概率...")
        
        # 获取预测概率
        pred_probabilities = self.classifier.predict(features, verbose=0)
        
        # 计算置信度 (距离决策边界的距离)
        # 对于二分类，置信度 = |预测概率 - 0.5| * 2
        confidence_scores = np.abs(pred_probabilities.flatten() - 0.5) * 2
        
        # 预测标签
        pred_labels = (pred_probabilities > 0.5).astype(int).flatten()
        
        # 计算准确性
        correct_predictions = (pred_labels == labels)
        
        # 分析置信度分布
        print(f"置信度统计:")
        print(f"- 最小置信度: {confidence_scores.min():.4f}")
        print(f"- 最大置信度: {confidence_scores.max():.4f}")
        print(f"- 平均置信度: {confidence_scores.mean():.4f}")
        print(f"- 置信度标准差: {confidence_scores.std():.4f}")
        
        # 按类别分析置信度
        control_confidence = confidence_scores[labels == 0]
        epilepsy_confidence = confidence_scores[labels == 1]
        
        print(f"控制组置信度: {control_confidence.mean():.4f} ± {control_confidence.std():.4f}")
        print(f"癫痫组置信度: {epilepsy_confidence.mean():.4f} ± {epilepsy_confidence.std():.4f}")
        
        return confidence_scores, pred_probabilities, pred_labels, correct_predictions
    
    def select_high_confidence_samples(self, features, labels, confidence_scores, 
                                     confidence_threshold=0.7, min_samples_per_class=1000):
        """
        选择高置信度样本
        """
        print(f"筛选高置信度样本 (阈值: {confidence_threshold})...")
        
        # 筛选高置信度样本
        high_confidence_mask = confidence_scores >= confidence_threshold
        
        # 分别筛选控制组和癫痫组的高置信度样本
        control_mask = (labels == 0) & high_confidence_mask
        epilepsy_mask = (labels == 1) & high_confidence_mask
        
        control_high_conf = features[control_mask]
        epilepsy_high_conf = features[epilepsy_mask]
        
        print(f"高置信度样本统计:")
        print(f"- 控制组高置信度样本: {len(control_high_conf)}")
        print(f"- 癫痫组高置信度样本: {len(epilepsy_high_conf)}")
        print(f"- 总高置信度样本: {len(control_high_conf) + len(epilepsy_high_conf)}")
        print(f"- 筛选比例: {(len(control_high_conf) + len(epilepsy_high_conf)) / len(features) * 100:.1f}%")
        
        # 检查样本数量是否足够
        if len(control_high_conf) < min_samples_per_class:
            print(f"⚠️  控制组高置信度样本不足 ({len(control_high_conf)} < {min_samples_per_class})")
            print(f"   降低置信度阈值到 {confidence_threshold - 0.1}")
            return self.select_high_confidence_samples(features, labels, confidence_scores, 
                                                     confidence_threshold - 0.1, min_samples_per_class)
        
        if len(epilepsy_high_conf) < min_samples_per_class:
            print(f"⚠️  癫痫组高置信度样本不足 ({len(epilepsy_high_conf)} < {min_samples_per_class})")
            print(f"   降低置信度阈值到 {confidence_threshold - 0.1}")
            return self.select_high_confidence_samples(features, labels, confidence_scores, 
                                                     confidence_threshold - 0.1, min_samples_per_class)
        
        return control_high_conf, epilepsy_high_conf, confidence_threshold
    
    def visualize_confidence_distribution(self, confidence_scores, labels, correct_predictions, 
                                        confidence_threshold=0.7):
        """
        可视化置信度分布
        """
        print("创建置信度分布可视化...")
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 整体置信度分布
        axes[0, 0].hist(confidence_scores, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].axvline(confidence_threshold, color='red', linestyle='--', linewidth=2, 
                          label=f'置信度阈值 ({confidence_threshold})')
        axes[0, 0].set_title('整体置信度分布', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('置信度')
        axes[0, 0].set_ylabel('样本数量')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 按类别的置信度分布
        control_conf = confidence_scores[labels == 0]
        epilepsy_conf = confidence_scores[labels == 1]
        
        axes[0, 1].hist(control_conf, bins=30, alpha=0.7, label='控制组', color='green')
        axes[0, 1].hist(epilepsy_conf, bins=30, alpha=0.7, label='癫痫组', color='red')
        axes[0, 1].axvline(confidence_threshold, color='black', linestyle='--', linewidth=2)
        axes[0, 1].set_title('按类别的置信度分布', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('置信度')
        axes[0, 1].set_ylabel('样本数量')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 置信度vs准确性
        correct_conf = confidence_scores[correct_predictions]
        incorrect_conf = confidence_scores[~correct_predictions]
        
        axes[1, 0].hist(correct_conf, bins=30, alpha=0.7, label='正确预测', color='green')
        axes[1, 0].hist(incorrect_conf, bins=30, alpha=0.7, label='错误预测', color='red')
        axes[1, 0].axvline(confidence_threshold, color='black', linestyle='--', linewidth=2)
        axes[1, 0].set_title('置信度vs预测准确性', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('置信度')
        axes[1, 0].set_ylabel('样本数量')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 置信度阈值vs样本保留率
        thresholds = np.arange(0.1, 1.0, 0.05)
        retention_rates = []
        accuracy_rates = []
        
        for thresh in thresholds:
            high_conf_mask = confidence_scores >= thresh
            retention_rate = np.sum(high_conf_mask) / len(confidence_scores)
            
            if np.sum(high_conf_mask) > 0:
                accuracy_rate = np.sum(correct_predictions[high_conf_mask]) / np.sum(high_conf_mask)
            else:
                accuracy_rate = 0
            
            retention_rates.append(retention_rate)
            accuracy_rates.append(accuracy_rate)
        
        ax1 = axes[1, 1]
        ax2 = ax1.twinx()
        
        line1 = ax1.plot(thresholds, retention_rates, 'b-', linewidth=2, label='样本保留率')
        line2 = ax2.plot(thresholds, accuracy_rates, 'r-', linewidth=2, label='准确率')
        
        ax1.axvline(confidence_threshold, color='black', linestyle='--', linewidth=2)
        ax1.set_xlabel('置信度阈值')
        ax1.set_ylabel('样本保留率', color='blue')
        ax2.set_ylabel('准确率', color='red')
        ax1.set_title('置信度阈值权衡分析', fontsize=14, fontweight='bold')
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='center right')
        
        ax1.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('置信度分析报告.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("置信度分布可视化完成!")


class GANLesionDetector:
    """
    GAN病灶识别器 (基于高置信度样本)
    """
    
    def __init__(self, input_dim=94):
        self.input_dim = input_dim
        self.latent_dim = 32
        
        self.generator = None
        self.discriminator = None
        self.gan = None
        
        print(f"GAN病灶识别器初始化:")
        print(f"- 输入维度: {input_dim}")
        print(f"- 潜在空间维度: {self.latent_dim}")
    
    def build_generator(self):
        """
        构建生成器 (癫痫特征 -> "健康"特征)
        """
        print("构建GAN生成器...")
        
        input_layer = keras.Input(shape=(self.input_dim,), name='epilepsy_features')
        
        # 编码器部分 - 提取病灶特征
        enc1 = layers.Dense(64, activation='relu')(input_layer)
        enc1 = layers.BatchNormalization()(enc1)
        enc1 = layers.Dropout(0.2)(enc1)
        
        enc2 = layers.Dense(48, activation='relu')(enc1)
        enc2 = layers.BatchNormalization()(enc2)
        enc2 = layers.Dropout(0.2)(enc2)
        
        # 瓶颈层 - 病灶特征编码
        bottleneck = layers.Dense(self.latent_dim, activation='relu', name='lesion_encoding')(enc2)
        bottleneck = layers.BatchNormalization()(bottleneck)
        
        # 解码器部分 - 生成"健康"特征
        dec1 = layers.Dense(48, activation='relu')(bottleneck)
        dec1 = layers.BatchNormalization()(dec1)
        dec1 = layers.Dropout(0.2)(dec1)
        
        dec2 = layers.Dense(64, activation='relu')(dec1)
        dec2 = layers.BatchNormalization()(dec2)
        dec2 = layers.Dropout(0.2)(dec2)
        
        # 输出层 - "伪装"的健康特征
        output = layers.Dense(self.input_dim, activation='linear', name='fake_healthy_features')(dec2)
        
        generator = keras.Model(input_layer, output, name='lesion_generator')
        
        self.generator = generator
        
        print(f"生成器构建完成: {generator.count_params():,} 参数")
        
        return generator
    
    def build_adversarial_model(self, discriminator):
        """
        构建对抗模型
        """
        print("构建对抗模型...")
        
        # 冻结判别器
        discriminator.trainable = False
        
        # 对抗模型: 生成器 -> 判别器
        epilepsy_input = keras.Input(shape=(self.input_dim,), name='epilepsy_input')
        fake_healthy = self.generator(epilepsy_input)
        discrimination_output = discriminator(fake_healthy)
        
        gan = keras.Model(epilepsy_input, discrimination_output, name='adversarial_lesion_detector')
        
        # 编译对抗模型
        gan.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.0002, beta_1=0.5),
            loss='binary_crossentropy',
            metrics=['accuracy']
        )
        
        self.gan = gan
        self.discriminator = discriminator
        
        print("对抗模型构建完成")
        
        return gan


class ConfidenceBasedGANSystem:
    """
    基于置信度的完整GAN系统
    """
    
    def __init__(self):
        self.confidence_selector = None
        self.gan_detector = None
        
        # 数据
        self.original_features = None
        self.original_labels = None
        self.high_conf_control = None
        self.high_conf_epilepsy = None
        self.confidence_threshold = None
        
        print("基于置信度的GAN系统初始化")
    
    def load_and_analyze_confidence(self, confidence_threshold=0.7):
        """
        加载数据并分析置信度
        """
        print("步骤1: 加载数据并分析置信度")
        
        # 初始化置信度选择器
        self.confidence_selector = ConfidenceBasedSampleSelector(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            'wavelet_classifier_results/feature_scaler.pkl',
            'wavelet_classifier_results/signal_scaler.pkl'
        )
        
        # 加载特征和标签
        self.original_features = np.load('wavelet_classifier_results/extracted_features.npy')
        self.original_labels = np.load('wavelet_classifier_results/labels.npy')
        
        print(f"原始数据: {len(self.original_features)} 样本")
        
        # 计算置信度
        confidence_scores, pred_probs, pred_labels, correct_preds = \
            self.confidence_selector.calculate_confidence_scores(self.original_features, self.original_labels)
        
        # 可视化置信度分布
        self.confidence_selector.visualize_confidence_distribution(
            confidence_scores, self.original_labels, correct_preds, confidence_threshold
        )
        
        # 选择高置信度样本
        self.high_conf_control, self.high_conf_epilepsy, self.confidence_threshold = \
            self.confidence_selector.select_high_confidence_samples(
                self.original_features, self.original_labels, confidence_scores, confidence_threshold
            )
        
        return confidence_scores, pred_probs, pred_labels, correct_preds


def main():
    """
    主函数 - 基于置信度的GAN病灶识别系统
    """
    print("=" * 80)
    print("基于置信度的GAN病灶识别系统")
    print("=" * 80)
    
    # 初始化系统
    system = ConfidenceBasedGANSystem()
    
    # 步骤1: 加载数据并分析置信度
    confidence_scores, pred_probs, pred_labels, correct_preds = \
        system.load_and_analyze_confidence(confidence_threshold=0.7)
    
    # 步骤2: 训练GAN病灶识别器
    print("\n步骤2: 训练GAN病灶识别器")
    gan_detector, training_history = train_confidence_based_gan(system)

    # 步骤3: 评估GAN性能
    print("\n步骤3: 评估GAN性能")
    evaluation_results = evaluate_gan_performance(system, gan_detector)

    # 步骤4: 病灶特征分析
    print("\n步骤4: 病灶特征分析")
    lesion_analysis = analyze_lesion_features(system, gan_detector)

    # 步骤5: 创建综合可视化
    print("\n步骤5: 创建综合可视化")
    create_comprehensive_visualizations(system, gan_detector, training_history, evaluation_results)

    # 步骤6: 保存结果
    print("\n步骤6: 保存结果")
    save_confidence_gan_results(system, gan_detector, confidence_scores, evaluation_results)

    print("\n" + "=" * 80)
    print("基于置信度的GAN病灶识别系统完成!")
    print("=" * 80)

    return system, gan_detector, confidence_scores


def train_confidence_based_gan(system):
    """
    训练基于置信度的GAN
    """
    print("训练基于置信度的GAN...")

    # 初始化GAN
    gan_detector = GANLesionDetector(input_dim=system.original_features.shape[1])
    generator = gan_detector.build_generator()

    # 使用预训练的分类器作为判别器
    discriminator = system.confidence_selector.classifier

    # 构建对抗模型
    gan = gan_detector.build_adversarial_model(discriminator)

    # 准备训练数据
    control_data = system.high_conf_control
    epilepsy_data = system.high_conf_epilepsy

    print(f"GAN训练数据:")
    print(f"- 高置信度控制组: {len(control_data)} 样本")
    print(f"- 高置信度癫痫组: {len(epilepsy_data)} 样本")
    print(f"- 置信度阈值: {system.confidence_threshold}")

    # 训练参数
    epochs = 100
    batch_size = 64

    # 训练历史
    training_history = {
        'generator_loss': [],
        'discriminator_accuracy': [],
        'fake_as_healthy_rate': [],
        'reconstruction_loss': []
    }

    print("开始对抗训练...")

    for epoch in range(epochs):
        # 随机选择癫痫样本批次
        if len(epilepsy_data) >= batch_size:
            idx = np.random.choice(len(epilepsy_data), batch_size, replace=False)
        else:
            idx = np.random.choice(len(epilepsy_data), batch_size, replace=True)

        real_epilepsy_batch = epilepsy_data[idx]

        # 训练生成器 (让生成的特征被分类器识别为健康)
        fake_healthy_labels = np.zeros((batch_size, 1))  # 目标: 被识别为控制组

        # 冻结判别器，训练生成器
        discriminator.trainable = False
        gen_loss = gan.train_on_batch(real_epilepsy_batch, fake_healthy_labels)

        # 评估生成器效果
        fake_healthy_features = generator.predict(real_epilepsy_batch, verbose=0)
        fake_predictions = discriminator.predict(fake_healthy_features, verbose=0)
        fake_as_healthy_rate = np.mean(fake_predictions < 0.5)  # 被识别为健康的比例

        # 计算重建损失
        reconstruction_loss = np.mean((real_epilepsy_batch - fake_healthy_features) ** 2)

        # 记录训练历史
        training_history['generator_loss'].append(gen_loss[0])
        training_history['discriminator_accuracy'].append(gen_loss[1])
        training_history['fake_as_healthy_rate'].append(fake_as_healthy_rate)
        training_history['reconstruction_loss'].append(reconstruction_loss)

        # 打印进度
        if (epoch + 1) % 10 == 0:
            print(f"Epoch {epoch+1}/{epochs}:")
            print(f"  生成器损失: {gen_loss[0]:.4f}")
            print(f"  伪装成功率: {fake_as_healthy_rate:.4f} ({fake_as_healthy_rate*100:.1f}%)")
            print(f"  重建损失: {reconstruction_loss:.4f}")

    print("GAN训练完成!")

    return gan_detector, training_history


def evaluate_gan_performance(system, gan_detector):
    """
    评估GAN性能
    """
    print("评估GAN性能...")

    generator = gan_detector.generator
    discriminator = system.confidence_selector.classifier

    # 使用测试数据评估
    test_epilepsy = system.high_conf_epilepsy[:500]  # 取一部分作为测试
    test_control = system.high_conf_control[:500]

    # 生成"健康"特征
    fake_healthy_features = generator.predict(test_epilepsy, verbose=0)

    # 分类器对原始癫痫特征的预测
    original_epilepsy_pred = discriminator.predict(test_epilepsy, verbose=0)
    original_as_epilepsy_rate = np.mean(original_epilepsy_pred > 0.5)

    # 分类器对生成特征的预测
    fake_healthy_pred = discriminator.predict(fake_healthy_features, verbose=0)
    fake_as_healthy_rate = np.mean(fake_healthy_pred < 0.5)

    # 分类器对真实控制组的预测 (作为基准)
    real_control_pred = discriminator.predict(test_control, verbose=0)
    real_as_healthy_rate = np.mean(real_control_pred < 0.5)

    # 计算特征相似性
    feature_similarity = calculate_feature_similarity(fake_healthy_features, test_control)

    # 计算重建质量
    reconstruction_quality = calculate_reconstruction_quality(test_epilepsy, fake_healthy_features)

    evaluation_results = {
        'original_as_epilepsy_rate': original_as_epilepsy_rate,
        'fake_as_healthy_rate': fake_as_healthy_rate,
        'real_as_healthy_rate': real_as_healthy_rate,
        'feature_similarity': feature_similarity,
        'reconstruction_quality': reconstruction_quality,
        'lesion_masking_effectiveness': fake_as_healthy_rate / original_as_epilepsy_rate if original_as_epilepsy_rate > 0 else 0
    }

    print(f"GAN性能评估结果:")
    print(f"- 原始癫痫特征被正确识别率: {original_as_epilepsy_rate:.4f} ({original_as_epilepsy_rate*100:.1f}%)")
    print(f"- 生成特征被识别为健康率: {fake_as_healthy_rate:.4f} ({fake_as_healthy_rate*100:.1f}%)")
    print(f"- 真实控制组被识别为健康率: {real_as_healthy_rate:.4f} ({real_as_healthy_rate*100:.1f}%)")
    print(f"- 特征相似性 (与真实控制组): {feature_similarity:.4f}")
    print(f"- 重建质量: {reconstruction_quality:.4f}")
    print(f"- 病灶掩盖效果: {evaluation_results['lesion_masking_effectiveness']:.4f}")

    # 评估等级
    if fake_as_healthy_rate > 0.8:
        performance_level = "优秀"
    elif fake_as_healthy_rate > 0.6:
        performance_level = "良好"
    elif fake_as_healthy_rate > 0.4:
        performance_level = "一般"
    else:
        performance_level = "需改进"

    print(f"- 总体性能等级: {performance_level}")

    return evaluation_results


def calculate_feature_similarity(fake_features, real_features):
    """
    计算生成特征与真实特征的相似性
    """
    # 使用余弦相似度
    fake_mean = np.mean(fake_features, axis=0)
    real_mean = np.mean(real_features, axis=0)

    # 归一化
    fake_norm = fake_mean / (np.linalg.norm(fake_mean) + 1e-8)
    real_norm = real_mean / (np.linalg.norm(real_mean) + 1e-8)

    # 余弦相似度
    similarity = np.dot(fake_norm, real_norm)

    return similarity


def calculate_reconstruction_quality(original_features, reconstructed_features):
    """
    计算重建质量
    """
    # 使用均方误差的倒数作为质量指标
    mse = np.mean((original_features - reconstructed_features) ** 2)
    quality = 1 / (1 + mse)  # 归一化到[0,1]

    return quality


def analyze_lesion_features(system, gan_detector):
    """
    分析病灶特征
    """
    print("分析病灶特征...")

    generator = gan_detector.generator

    # 获取病灶编码器
    lesion_encoder = keras.Model(
        generator.input,
        generator.get_layer('lesion_encoding').output,
        name='lesion_encoder'
    )

    # 提取病灶特征
    sample_epilepsy = system.high_conf_epilepsy[:1000]
    lesion_features = lesion_encoder.predict(sample_epilepsy, verbose=0)

    # 分析病灶特征
    lesion_analysis = {
        'feature_dim': lesion_features.shape[1],
        'feature_mean': np.mean(lesion_features, axis=0),
        'feature_std': np.std(lesion_features, axis=0),
        'feature_range': [lesion_features.min(), lesion_features.max()],
        'dominant_features': np.argsort(np.std(lesion_features, axis=0))[-5:]  # 最变化的5个特征
    }

    print(f"病灶特征分析:")
    print(f"- 特征维度: {lesion_analysis['feature_dim']}")
    print(f"- 特征范围: [{lesion_analysis['feature_range'][0]:.3f}, {lesion_analysis['feature_range'][1]:.3f}]")
    print(f"- 最具区分性的特征索引: {lesion_analysis['dominant_features']}")

    return lesion_analysis


def create_comprehensive_visualizations(system, gan_detector, training_history, evaluation_results):
    """
    创建综合可视化
    """
    print("创建综合可视化...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. GAN训练历史
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))

    epochs = range(1, len(training_history['generator_loss']) + 1)

    # 生成器损失
    axes[0, 0].plot(epochs, training_history['generator_loss'], 'b-', linewidth=2)
    axes[0, 0].set_title('生成器损失', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('训练轮数')
    axes[0, 0].set_ylabel('损失值')
    axes[0, 0].grid(True, alpha=0.3)

    # 伪装成功率
    axes[0, 1].plot(epochs, training_history['fake_as_healthy_rate'], 'g-', linewidth=2)
    axes[0, 1].axhline(y=0.8, color='red', linestyle='--', label='优秀阈值')
    axes[0, 1].set_title('病灶伪装成功率', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('训练轮数')
    axes[0, 1].set_ylabel('伪装成功率')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 重建损失
    axes[1, 0].plot(epochs, training_history['reconstruction_loss'], 'r-', linewidth=2)
    axes[1, 0].set_title('重建损失', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('训练轮数')
    axes[1, 0].set_ylabel('重建损失')
    axes[1, 0].grid(True, alpha=0.3)

    # 性能总结
    performance_metrics = [
        evaluation_results['original_as_epilepsy_rate'],
        evaluation_results['fake_as_healthy_rate'],
        evaluation_results['real_as_healthy_rate'],
        evaluation_results['feature_similarity'],
        evaluation_results['reconstruction_quality']
    ]

    metric_names = ['原始癫痫\n识别率', '生成特征\n健康率', '真实控制组\n健康率',
                   '特征\n相似性', '重建\n质量']

    bars = axes[1, 1].bar(metric_names, performance_metrics,
                         color=['red', 'green', 'blue', 'orange', 'purple'])
    axes[1, 1].set_title('GAN性能总结', fontsize=14, fontweight='bold')
    axes[1, 1].set_ylabel('分数')
    axes[1, 1].set_ylim(0, 1)

    # 添加数值标签
    for bar, value in zip(bars, performance_metrics):
        axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('基于置信度的GAN训练结果.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("综合可视化创建完成!")


def save_confidence_gan_results(system, gan_detector, confidence_scores, evaluation_results):
    """
    保存基于置信度的GAN结果
    """
    print("保存基于置信度的GAN结果...")

    # 创建结果目录
    os.makedirs('confidence_gan_results', exist_ok=True)

    # 保存模型
    gan_detector.generator.save('confidence_gan_results/confidence_based_generator.keras')
    gan_detector.gan.save('confidence_gan_results/confidence_based_gan.keras')

    # 保存置信度分析结果
    confidence_analysis = {
        'confidence_threshold': system.confidence_threshold,
        'original_samples': len(system.original_features),
        'high_conf_control_samples': len(system.high_conf_control),
        'high_conf_epilepsy_samples': len(system.high_conf_epilepsy),
        'retention_rate': (len(system.high_conf_control) + len(system.high_conf_epilepsy)) / len(system.original_features),
        'confidence_stats': {
            'min': float(confidence_scores.min()),
            'max': float(confidence_scores.max()),
            'mean': float(confidence_scores.mean()),
            'std': float(confidence_scores.std())
        }
    }

    # 保存评估结果
    results_summary = {
        'confidence_analysis': confidence_analysis,
        'gan_performance': evaluation_results,
        'system_config': {
            'input_dim': gan_detector.input_dim,
            'latent_dim': gan_detector.latent_dim,
            'training_epochs': 100,
            'batch_size': 64
        }
    }

    with open('confidence_gan_results/results_summary.json', 'w', encoding='utf-8') as f:
        json.dump(results_summary, f, indent=2, ensure_ascii=False)

    # 保存高置信度样本
    np.save('confidence_gan_results/high_conf_control_features.npy', system.high_conf_control)
    np.save('confidence_gan_results/high_conf_epilepsy_features.npy', system.high_conf_epilepsy)
    np.save('confidence_gan_results/confidence_scores.npy', confidence_scores)

    print("基于置信度的GAN结果保存完成!")
    print("保存的文件:")
    print("- confidence_based_generator.keras (生成器模型)")
    print("- confidence_based_gan.keras (完整GAN模型)")
    print("- results_summary.json (结果总结)")
    print("- high_conf_*_features.npy (高置信度样本)")
    print("- confidence_scores.npy (置信度分数)")


if __name__ == "__main__":
    system, gan_detector, confidence_scores = main()
