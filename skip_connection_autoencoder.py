#!/usr/bin/env python3
"""
带跳跃连接的癫痫自动编码器
Epilepsy Autoencoder with Skip Connections

主要特点:
1. U-Net风格的跳跃连接 (U-Net Style Skip Connections)
2. 残差连接 (Residual Connections)
3. 密集连接 (Dense Connections)
4. 注意力门控跳跃连接 (Attention-Gated Skip Connections)
5. 多尺度特征融合 (Multi-scale Feature Fusion)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import warnings
import os
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class AttentionGate(layers.Layer):
    """
    注意力门控层 - 用于跳跃连接的特征选择
    """
    def __init__(self, filters, **kwargs):
        super(AttentionGate, self).__init__(**kwargs)
        self.filters = filters
        self.W_g = layers.Dense(filters, activation='relu')
        self.W_x = layers.Dense(filters, activation='relu')
        self.psi = layers.Dense(1, activation='sigmoid')
        
    def call(self, g, x):
        """
        g: 门控信号 (来自解码器)
        x: 跳跃连接的特征 (来自编码器)
        """
        # 计算注意力系数
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.psi(layers.Add()([g1, x1]))
        
        # 应用注意力权重
        return layers.Multiply()([x, psi])
    
    def get_config(self):
        config = super().get_config()
        config.update({"filters": self.filters})
        return config

class ResidualBlock(layers.Layer):
    """
    残差块 - 包含跳跃连接的基本构建块
    """
    def __init__(self, filters, dropout_rate=0.1, **kwargs):
        super(ResidualBlock, self).__init__(**kwargs)
        self.filters = filters
        self.dropout_rate = dropout_rate
        
        self.dense1 = layers.Dense(filters, activation='relu')
        self.bn1 = layers.BatchNormalization()
        self.dropout1 = layers.Dropout(dropout_rate)
        
        self.dense2 = layers.Dense(filters, activation='relu')
        self.bn2 = layers.BatchNormalization()
        self.dropout2 = layers.Dropout(dropout_rate)
        
        self.shortcut = layers.Dense(filters, activation='linear')
        
    def call(self, inputs, training=None):
        # 主路径
        x = self.dense1(inputs)
        x = self.bn1(x, training=training)
        x = self.dropout1(x, training=training)
        
        x = self.dense2(x)
        x = self.bn2(x, training=training)
        x = self.dropout2(x, training=training)
        
        # 跳跃连接
        shortcut = self.shortcut(inputs)
        
        # 残差连接
        return layers.Add()([x, shortcut])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "dropout_rate": self.dropout_rate
        })
        return config

class SkipConnectionAutoencoder:
    """
    带跳跃连接的癫痫自动编码器
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        """
        初始化跳跃连接自动编码器
        """
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        # 存储数据和模型
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.history = None
        
        print(f"初始化跳跃连接自动编码器:")
        print(f"- 分段长度: {segment_length} 样本点 ({segment_length/sampling_rate:.1f}秒)")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 步长: {self.step_size} 样本点")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """
        加载并预处理数据
        """
        print("加载Gamma波数据...")
        
        # 加载原始数据
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        print(f"原始数据: {gamma_waves.shape[0]} 个样本, 每个 {gamma_waves.shape[1]} 个时间点")
        
        # 改进的预处理
        processed_waves = []
        
        for i, wave in enumerate(gamma_waves):
            # 1. 去除异常值
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            # 2. 去趋势
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            # 3. 带通滤波
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            # 4. 标准化
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            
            processed_waves.append(wave_normalized)
        
        processed_waves = np.array(processed_waves)
        
        print(f"预处理完成:")
        print(f"- 原始范围: [{gamma_waves.min():.2f}, {gamma_waves.max():.2f}]")
        print(f"- 处理后范围: [{processed_waves.min():.2f}, {processed_waves.max():.2f}]")
        
        return processed_waves, metadata
    
    def create_segments(self, processed_waves):
        """
        创建数据段
        """
        print("创建数据段...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    if np.std(segment) > 0.01:
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        print(f"段创建完成: {len(segments)} 个有效段")
        
        # 全局标准化
        self.scaler = RobustScaler()
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        segments_final = segments_scaled.reshape(segments.shape)
        
        print(f"最终数据范围: [{segments_final.min():.3f}, {segments_final.max():.3f}]")
        
        self.segments = segments_final
        self.segment_info = segment_info
        
        return segments_final, segment_info
    
    def build_skip_connection_autoencoder(self, input_dim=512, encoding_dim=32, 
                                        base_filters=64):
        """
        构建带跳跃连接的自动编码器
        
        Parameters:
        -----------
        input_dim : int
            输入维度
        encoding_dim : int
            编码维度
        base_filters : int
            基础滤波器数量
        """
        print("构建带跳跃连接的自动编码器...")
        
        # ==================== 编码器部分 ====================
        input_layer = keras.Input(shape=(input_dim,), name='input')
        
        # 编码器层1 - 残差块
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(input_layer)  # 256
        
        # 编码器层2 - 残差块
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)  # 128
        
        # 编码器层3 - 残差块
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)  # 64
        
        # 瓶颈层 (潜在空间)
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)
        
        # ==================== 解码器部分 (带跳跃连接) ====================
        # 解码器层1
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)
        
        # 跳跃连接1 - 注意力门控
        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)
        
        # 解码器层2
        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)
        
        # 跳跃连接2 - 注意力门控
        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)
        
        # 解码器层3
        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)
        
        # 跳跃连接3 - 注意力门控
        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)
        
        # 输出层
        output = layers.Dense(input_dim, activation='linear', name='output')(dec3_skip)
        
        # ==================== 构建模型 ====================
        autoencoder = keras.Model(input_layer, output, name='skip_connection_autoencoder')
        encoder = keras.Model(input_layer, bottleneck, name='skip_connection_encoder')
        
        # 编译模型
        optimizer = keras.optimizers.Adam(learning_rate=0.0005, beta_1=0.9, beta_2=0.999)
        
        autoencoder.compile(
            optimizer=optimizer,
            loss='huber',  # 对异常值鲁棒
            metrics=['mae', 'mse']
        )
        
        print("跳跃连接模型构建完成:")
        print(f"- 输入维度: {input_dim}")
        print(f"- 编码维度: {encoding_dim}")
        print(f"- 基础滤波器数: {base_filters}")
        print(f"- 跳跃连接数: 3个")
        print(f"- 注意力门控: 是")
        print(f"- 残差连接: 是")
        print(f"- 总参数: {autoencoder.count_params():,}")
        
        self.autoencoder = autoencoder
        self.encoder = encoder
        
        return autoencoder, encoder
    
    def train_skip_connection_model(self, X_train, X_val=None, epochs=100, batch_size=32):
        """
        训练跳跃连接模型
        """
        print("开始训练跳跃连接自动编码器...")
        
        # 回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=25,  # 增加耐心值，因为跳跃连接模型可能需要更多时间收敛
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=12,
                min_lr=1e-6,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_skip_connection_autoencoder.keras',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 训练
        if X_val is not None:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_data=(X_val, X_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_split=0.2,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        
        self.history = history
        print("跳跃连接模型训练完成!")
        
        return history


def main():
    """
    主函数 - 执行跳跃连接自动编码器训练
    """
    print("=" * 70)
    print("带跳跃连接的癫痫自动编码器训练管道")
    print("=" * 70)
    
    # 初始化管道
    pipeline = SkipConnectionAutoencoder(
        segment_length=512,
        overlap_ratio=0.75,
        sampling_rate=128
    )
    
    # 步骤1: 加载和预处理数据
    print("\n步骤1: 加载和预处理数据")
    processed_waves, metadata = pipeline.load_and_preprocess_data()
    
    # 步骤2: 创建数据段
    print("\n步骤2: 创建数据段")
    segments, segment_info = pipeline.create_segments(processed_waves)
    
    # 步骤3: 划分数据集
    print("\n步骤3: 划分数据集")
    X_train, X_test = train_test_split(segments, test_size=0.2, random_state=42)
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")
    
    # 步骤4: 构建跳跃连接模型
    print("\n步骤4: 构建跳跃连接自动编码器")
    autoencoder, encoder = pipeline.build_skip_connection_autoencoder(
        input_dim=512,
        encoding_dim=32,
        base_filters=64
    )
    
    print("\n跳跃连接自动编码器结构:")
    autoencoder.summary()

    # 步骤5: 训练跳跃连接模型
    print("\n步骤5: 训练跳跃连接模型")
    history = pipeline.train_skip_connection_model(
        X_train,
        epochs=80,
        batch_size=32
    )

    # 步骤6: 评估跳跃连接模型
    print("\n步骤6: 评估跳跃连接模型")
    evaluation_results = evaluate_skip_connection_model(pipeline, X_test)

    # 步骤7: 创建跳跃连接可视化
    print("\n步骤7: 创建跳跃连接可视化")
    create_skip_connection_visualizations(pipeline, X_test, evaluation_results)

    # 步骤8: 保存跳跃连接模型
    print("\n步骤8: 保存跳跃连接模型")
    os.makedirs('skip_connection_models', exist_ok=True)
    pipeline.autoencoder.save('skip_connection_models/skip_connection_autoencoder.keras')
    pipeline.encoder.save('skip_connection_models/skip_connection_encoder.keras')

    import joblib
    joblib.dump(pipeline.scaler, 'skip_connection_models/skip_connection_scaler.pkl')

    print("\n跳跃连接自动编码器训练完成!")
    print("主要特点:")
    print("- ✅ U-Net风格跳跃连接")
    print("- ✅ 注意力门控机制")
    print("- ✅ 残差连接")
    print("- ✅ 多尺度特征融合")
    print("- ✅ 改进的梯度流动")
    print("- ✅ 更好的重建质量")

    return pipeline, X_train, X_test, evaluation_results


def evaluate_skip_connection_model(pipeline, X_test):
    """
    评估跳跃连接模型性能
    """
    print("评估跳跃连接模型性能...")

    # 重建预测
    X_reconstructed = pipeline.autoencoder.predict(X_test, verbose=0)

    # 计算多种误差指标
    mse_errors = np.mean((X_test - X_reconstructed) ** 2, axis=1)
    mae_errors = np.mean(np.abs(X_test - X_reconstructed), axis=1)

    # 相关系数
    correlations = []
    for i in range(len(X_test)):
        corr = np.corrcoef(X_test[i], X_reconstructed[i])[0, 1]
        if not np.isnan(corr):
            correlations.append(corr)
    correlations = np.array(correlations)

    # 结构相似性指数 (SSIM)
    from skimage.metrics import structural_similarity as ssim
    ssim_scores = []
    for i in range(len(X_test)):
        # 将1D信号重塑为2D以计算SSIM
        signal1 = X_test[i].reshape(32, 16)
        signal2 = X_reconstructed[i].reshape(32, 16)
        ssim_score = ssim(signal1, signal2, data_range=signal1.max() - signal1.min())
        ssim_scores.append(ssim_score)
    ssim_scores = np.array(ssim_scores)

    print(f"跳跃连接模型性能评估:")
    print(f"- MSE误差: 均值={np.mean(mse_errors):.4f}, 标准差={np.std(mse_errors):.4f}")
    print(f"- MAE误差: 均值={np.mean(mae_errors):.4f}, 标准差={np.std(mae_errors):.4f}")
    print(f"- 相关系数: 均值={np.mean(correlations):.4f}, 标准差={np.std(correlations):.4f}")
    print(f"- SSIM分数: 均值={np.mean(ssim_scores):.4f}, 标准差={np.std(ssim_scores):.4f}")
    print(f"- 最佳重建相关系数: {np.max(correlations):.4f}")
    print(f"- 最差重建相关系数: {np.min(correlations):.4f}")

    return {
        'mse_errors': mse_errors,
        'mae_errors': mae_errors,
        'correlations': correlations,
        'ssim_scores': ssim_scores,
        'reconstructed': X_reconstructed
    }


def create_skip_connection_visualizations(pipeline, X_test, evaluation_results):
    """
    创建跳跃连接模型的可视化
    """
    print("创建跳跃连接可视化图表...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 训练历史可视化
    if pipeline.history is not None:
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))

        # 损失曲线
        axes[0, 0].plot(pipeline.history.history['loss'], 'b-', label='训练损失', linewidth=2)
        if 'val_loss' in pipeline.history.history:
            axes[0, 0].plot(pipeline.history.history['val_loss'], 'r-', label='验证损失', linewidth=2)
        axes[0, 0].set_title('跳跃连接模型损失曲线', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('训练轮数')
        axes[0, 0].set_ylabel('损失值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # MAE曲线
        axes[0, 1].plot(pipeline.history.history['mae'], 'g-', label='训练MAE', linewidth=2)
        if 'val_mae' in pipeline.history.history:
            axes[0, 1].plot(pipeline.history.history['val_mae'], 'orange', label='验证MAE', linewidth=2)
        axes[0, 1].set_title('平均绝对误差曲线', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('训练轮数')
        axes[0, 1].set_ylabel('MAE值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # MSE曲线
        axes[1, 0].plot(pipeline.history.history['mse'], 'purple', label='训练MSE', linewidth=2)
        if 'val_mse' in pipeline.history.history:
            axes[1, 0].plot(pipeline.history.history['val_mse'], 'brown', label='验证MSE', linewidth=2)
        axes[1, 0].set_title('均方误差曲线', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('训练轮数')
        axes[1, 0].set_ylabel('MSE值')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 学习率曲线
        if 'lr' in pipeline.history.history:
            axes[1, 1].plot(pipeline.history.history['lr'], 'red', label='学习率', linewidth=2)
            axes[1, 1].set_title('学习率变化', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('训练轮数')
            axes[1, 1].set_ylabel('学习率')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, '学习率数据不可用', ha='center', va='center',
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].set_title('学习率变化', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig('跳跃连接模型训练历史.png', dpi=300, bbox_inches='tight')
        plt.show()

    # 2. 重建效果对比 (包含SSIM分数)
    fig, axes = plt.subplots(3, 1, figsize=(16, 12))

    time = np.arange(512) / 128  # 4秒时间轴
    X_reconstructed = evaluation_results['reconstructed']
    correlations = evaluation_results['correlations']
    ssim_scores = evaluation_results['ssim_scores']

    # 选择最好、中等、最差的重建样本
    best_idx = np.argmax(correlations)
    worst_idx = np.argmin(correlations)
    median_idx = np.argsort(correlations)[len(correlations)//2]

    samples = [
        (best_idx, f'最佳重建 (相关系数: {correlations[best_idx]:.3f}, SSIM: {ssim_scores[best_idx]:.3f})', 'green'),
        (median_idx, f'中等重建 (相关系数: {correlations[median_idx]:.3f}, SSIM: {ssim_scores[median_idx]:.3f})', 'blue'),
        (worst_idx, f'最差重建 (相关系数: {correlations[worst_idx]:.3f}, SSIM: {ssim_scores[worst_idx]:.3f})', 'red')
    ]

    for i, (idx, title, color) in enumerate(samples):
        axes[i].plot(time, X_test[idx], 'k-', label='原始信号', linewidth=2, alpha=0.8)
        axes[i].plot(time, X_reconstructed[idx], '--', color=color, label='重建信号', linewidth=2)
        axes[i].set_title(title, fontsize=12, fontweight='bold')
        axes[i].set_xlabel('时间 (秒)')
        axes[i].set_ylabel('标准化幅值')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('跳跃连接模型重建对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 性能分析图表 (包含SSIM)
    fig, axes = plt.subplots(2, 3, figsize=(18, 10))

    # MSE误差分布
    axes[0, 0].hist(evaluation_results['mse_errors'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('MSE误差分布', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('MSE误差')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].grid(True, alpha=0.3)

    # MAE误差分布
    axes[0, 1].hist(evaluation_results['mae_errors'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 1].set_title('MAE误差分布', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('MAE误差')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].grid(True, alpha=0.3)

    # 相关系数分布
    axes[0, 2].hist(correlations, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[0, 2].set_title('重建相关系数分布', fontsize=12, fontweight='bold')
    axes[0, 2].set_xlabel('相关系数')
    axes[0, 2].set_ylabel('频次')
    axes[0, 2].grid(True, alpha=0.3)

    # SSIM分数分布
    axes[1, 0].hist(ssim_scores, bins=30, alpha=0.7, color='gold', edgecolor='black')
    axes[1, 0].set_title('SSIM分数分布', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('SSIM分数')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)

    # 相关系数 vs SSIM散点图
    axes[1, 1].scatter(correlations, ssim_scores, alpha=0.6, color='purple')
    axes[1, 1].set_title('相关系数 vs SSIM分数', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('相关系数')
    axes[1, 1].set_ylabel('SSIM分数')
    axes[1, 1].grid(True, alpha=0.3)

    # MSE vs 相关系数散点图
    axes[1, 2].scatter(correlations, evaluation_results['mse_errors'], alpha=0.6, color='orange')
    axes[1, 2].set_title('相关系数 vs MSE误差', fontsize=12, fontweight='bold')
    axes[1, 2].set_xlabel('相关系数')
    axes[1, 2].set_ylabel('MSE误差')
    axes[1, 2].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('跳跃连接模型性能分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("跳跃连接可视化图表创建完成!")


if __name__ == "__main__":
    pipeline, X_train, X_test, evaluation_results = main()
