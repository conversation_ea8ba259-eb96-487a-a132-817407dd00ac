#!/usr/bin/env python3
"""
全面病灶检测管道 - 多通道多波段分析
Comprehensive Lesion Detection Pipeline - Multi-Channel Multi-Band Analysis

功能:
1. 正确加载控制组和癫痫组数据 (基于真实标签)
2. 对所有通道和所有频率波段进行病灶检测
3. 每个模型训练200轮
4. 生成详细的病灶定位报告
5. 识别每个患者的具体病灶位置
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import warnings
import os
import gzip
import json
from pathlib import Path
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 导入V2模型组件
from lesion_detection_pipeline import (
    ImprovedSmoothLoss, AttentionGate, ResidualBlock
)

class ComprehensiveLesionDetector:
    """
    全面病灶检测器 - 多通道多波段分析
    """
    
    def __init__(self, sampling_rate=128):
        self.sampling_rate = sampling_rate
        
        # EEG通道定义
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # 频率波段定义
        self.frequency_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8), 
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 60)
        }
        
        # 存储所有模型和结果
        self.models = {}  # {(channel, band): model}
        self.scalers = {}  # {(channel, band): scaler}
        self.detection_results = {}  # {(channel, band): results}
        
        # 数据存储
        self.control_data = {}  # {(channel, band): segments}
        self.epilepsy_data = {}  # {(channel, band): segments}
        self.patient_metadata = None
        
        print(f"全面病灶检测器初始化:")
        print(f"- 通道数: {len(self.eeg_channels)}")
        print(f"- 频率波段数: {len(self.frequency_bands)}")
        print(f"- 总模型数: {len(self.eeg_channels) * len(self.frequency_bands)}")
        print(f"- 每个模型训练轮数: 200")
    
    def load_real_labeled_data(self):
        """
        加载真实标签的控制组和癫痫组数据
        """
        print("加载真实标签的EEG数据...")
        
        # 加载Nigeria数据集元数据
        nigeria_metadata_path = "1252141/metadata_nigeria.csv"
        if not os.path.exists(nigeria_metadata_path):
            print("❌ 未找到Nigeria元数据文件")
            return False
        
        metadata = pd.read_csv(nigeria_metadata_path)
        
        # 分离控制组和癫痫组
        control_files = metadata[metadata['Group'] == 'control']['csv.file'].tolist()
        epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        
        print(f"数据集统计:")
        print(f"- 控制组文件数: {len(control_files)}")
        print(f"- 癫痫组文件数: {len(epilepsy_files)}")
        
        # 加载控制组数据
        print("加载控制组数据...")
        control_raw_data = self.load_eeg_files(control_files, "1252141/EEGs_Nigeria")
        
        # 加载癫痫组数据
        print("加载癫痫组数据...")
        epilepsy_raw_data = self.load_eeg_files(epilepsy_files, "1252141/EEGs_Nigeria")
        
        if len(control_raw_data) == 0 or len(epilepsy_raw_data) == 0:
            print("❌ 数据加载失败")
            return False
        
        # 保存患者元数据
        self.patient_metadata = metadata[metadata['Group'] == 'epilepsy'].copy()
        
        print(f"数据加载完成:")
        print(f"- 控制组样本: {len(control_raw_data)}")
        print(f"- 癫痫组样本: {len(epilepsy_raw_data)}")
        
        # 提取所有通道和频率波段的特征
        self.extract_all_channel_band_features(control_raw_data, epilepsy_raw_data)
        
        return True
    
    def load_eeg_files(self, file_list, data_dir):
        """
        加载EEG文件列表
        """
        loaded_data = []
        
        for i, filename in enumerate(file_list[:20]):  # 限制数量以节省时间
            file_path = os.path.join(data_dir, filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                        
                    # 提取EEG通道数据
                    eeg_data = df[self.eeg_channels].apply(pd.to_numeric, errors='coerce').values
                    
                    if not np.isnan(eeg_data).all():
                        loaded_data.append({
                            'filename': filename,
                            'data': eeg_data,
                            'channels': self.eeg_channels
                        })
                        
                        if (i + 1) % 5 == 0:
                            print(f"  已加载 {i + 1}/{min(20, len(file_list))} 个文件")
                
                except Exception as e:
                    print(f"  跳过文件 {filename}: {e}")
        
        return loaded_data
    
    def extract_all_channel_band_features(self, control_raw_data, epilepsy_raw_data):
        """
        提取所有通道和频率波段的特征
        """
        print("提取所有通道和频率波段的特征...")
        
        from scipy import signal
        
        total_combinations = len(self.eeg_channels) * len(self.frequency_bands)
        current_combination = 0
        
        for channel_idx, channel_name in enumerate(self.eeg_channels):
            for band_name, (low_freq, high_freq) in self.frequency_bands.items():
                current_combination += 1
                print(f"处理 {channel_name}-{band_name} ({current_combination}/{total_combinations})")
                
                # 提取控制组特征
                control_segments = self.extract_channel_band_segments(
                    control_raw_data, channel_idx, low_freq, high_freq
                )
                
                # 提取癫痫组特征
                epilepsy_segments = self.extract_channel_band_segments(
                    epilepsy_raw_data, channel_idx, low_freq, high_freq
                )
                
                if len(control_segments) > 0 and len(epilepsy_segments) > 0:
                    # 标准化
                    scaler = RobustScaler()
                    control_segments_scaled = scaler.fit_transform(control_segments.reshape(-1, 1)).reshape(control_segments.shape)
                    epilepsy_segments_scaled = scaler.transform(epilepsy_segments.reshape(-1, 1)).reshape(epilepsy_segments.shape)
                    
                    # 存储数据
                    key = (channel_name, band_name)
                    self.control_data[key] = control_segments_scaled
                    self.epilepsy_data[key] = epilepsy_segments_scaled
                    self.scalers[key] = scaler
                    
                    print(f"  控制组段数: {len(control_segments_scaled)}")
                    print(f"  癫痫组段数: {len(epilepsy_segments_scaled)}")
        
        print(f"特征提取完成，共处理 {len(self.control_data)} 个通道-波段组合")
    
    def extract_channel_band_segments(self, raw_data_list, channel_idx, low_freq, high_freq):
        """
        提取特定通道和频率波段的数据段
        """
        from scipy import signal
        
        all_segments = []
        segment_length = 512  # 4秒 @ 128Hz
        step_size = 128  # 1秒步长，75%重叠
        
        for data_item in raw_data_list:
            eeg_data = data_item['data']
            
            if eeg_data.shape[1] > channel_idx:
                channel_data = eeg_data[:, channel_idx]
                
                # 预处理
                channel_data = self.preprocess_channel_data(channel_data, low_freq, high_freq)
                
                # 创建段
                n_segments = (len(channel_data) - segment_length) // step_size + 1
                
                for i in range(n_segments):
                    start_idx = i * step_size
                    end_idx = start_idx + segment_length
                    
                    if end_idx <= len(channel_data):
                        segment = channel_data[start_idx:end_idx]
                        
                        if np.std(segment) > 0.01:  # 过滤低变异段
                            all_segments.append(segment)
        
        return np.array(all_segments) if all_segments else np.array([])
    
    def preprocess_channel_data(self, channel_data, low_freq, high_freq):
        """
        预处理单通道数据
        """
        from scipy import signal
        
        # 去除NaN值
        channel_data = pd.Series(channel_data).fillna(method='ffill').fillna(method='bfill').values
        
        # 去除异常值
        mean_val = np.mean(channel_data)
        std_val = np.std(channel_data)
        channel_data = np.clip(channel_data, mean_val - 3*std_val, mean_val + 3*std_val)
        
        # 去趋势
        x = np.arange(len(channel_data))
        coeffs = np.polyfit(x, channel_data, 1)
        trend = np.polyval(coeffs, x)
        channel_data = channel_data - trend
        
        # 带通滤波
        nyquist = self.sampling_rate / 2
        low_norm = low_freq / nyquist
        high_norm = high_freq / nyquist
        
        if low_norm < 1 and high_norm < 1:
            b, a = signal.butter(4, [low_norm, high_norm], btype='band')
            channel_data = signal.filtfilt(b, a, channel_data)
        
        # 标准化
        channel_data = (channel_data - np.mean(channel_data)) / (np.std(channel_data) + 1e-8)
        
        return channel_data

    def build_v2_autoencoder(self, input_dim=512, encoding_dim=64, base_filters=64):
        """
        构建V2自动编码器 (与确定的最佳模型相同)
        """
        input_layer = keras.Input(shape=(input_dim,), name='input')

        # 编码器
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(input_layer)
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)

        # 瓶颈层
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)

        # 解码器
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)

        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)

        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)

        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)

        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)

        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)

        output = layers.Dense(input_dim, activation='linear', name='output')(dec3_skip)

        # 构建模型
        autoencoder = keras.Model(input_layer, output, name='comprehensive_autoencoder_v2')

        # 使用改进的平滑性损失函数
        improved_smooth_loss = ImprovedSmoothLoss(
            reconstruction_weight=1.0,
            smoothness_weight=0.05,
            curvature_weight=0.02
        )

        optimizer = keras.optimizers.Adam(learning_rate=0.0005)

        autoencoder.compile(
            optimizer=optimizer,
            loss=improved_smooth_loss,
            metrics=['mae', 'mse']
        )

        return autoencoder

    def train_all_models(self, epochs=200):
        """
        训练所有通道-波段组合的模型 (每个200轮)
        """
        print(f"开始训练所有模型 (每个{epochs}轮)...")

        total_models = len(self.control_data)
        current_model = 0

        for key, control_segments in self.control_data.items():
            current_model += 1
            channel_name, band_name = key

            print(f"\n训练模型 {current_model}/{total_models}: {channel_name}-{band_name}")
            print(f"控制组段数: {len(control_segments)}")

            if len(control_segments) < 10:
                print(f"  ⚠️  数据量不足，跳过此组合")
                continue

            # 构建模型
            autoencoder = self.build_v2_autoencoder()

            # 划分训练验证集
            X_train, X_val = train_test_split(control_segments, test_size=0.2, random_state=42)

            # 训练回调
            callbacks = [
                keras.callbacks.EarlyStopping(
                    monitor='val_loss',
                    patience=30,
                    restore_best_weights=True,
                    verbose=0
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss',
                    factor=0.7,
                    patience=15,
                    min_lr=1e-7,
                    verbose=0
                )
            ]

            # 训练模型
            history = autoencoder.fit(
                X_train, X_train,
                validation_data=(X_val, X_val),
                epochs=epochs,
                batch_size=32,
                callbacks=callbacks,
                verbose=0
            )

            # 保存模型
            self.models[key] = autoencoder

            print(f"  ✅ 训练完成 - 轮数: {len(history.history['loss'])}, 最终损失: {history.history['val_loss'][-1]:.4f}")

        print(f"\n所有模型训练完成! 成功训练 {len(self.models)} 个模型")

    def detect_lesions_comprehensive(self):
        """
        全面病灶检测 - 所有通道和波段
        """
        print("开始全面病灶检测...")

        for key, epilepsy_segments in self.epilepsy_data.items():
            if key not in self.models:
                continue

            channel_name, band_name = key
            model = self.models[key]
            control_segments = self.control_data[key]

            print(f"检测 {channel_name}-{band_name}...")

            # 重建癫痫组数据
            epilepsy_reconstructed = model.predict(epilepsy_segments, verbose=0)

            # 计算重建误差
            reconstruction_errors = np.mean((epilepsy_segments - epilepsy_reconstructed) ** 2, axis=1)

            # 计算控制组基准
            control_reconstructed = model.predict(control_segments, verbose=0)
            control_errors = np.mean((control_segments - control_reconstructed) ** 2, axis=1)

            # 设置阈值 (控制组95%分位数)
            threshold = np.percentile(control_errors, 95)

            # 识别病灶
            lesion_mask = reconstruction_errors > threshold
            lesion_indices = np.where(lesion_mask)[0]

            # 存储结果
            self.detection_results[key] = {
                'reconstruction_errors': reconstruction_errors,
                'control_errors': control_errors,
                'threshold': threshold,
                'lesion_mask': lesion_mask,
                'lesion_indices': lesion_indices,
                'lesion_ratio': len(lesion_indices) / len(epilepsy_segments),
                'reconstructed_signals': epilepsy_reconstructed
            }

            print(f"  病灶段数: {len(lesion_indices)}/{len(epilepsy_segments)} ({len(lesion_indices)/len(epilepsy_segments)*100:.1f}%)")

        print("全面病灶检测完成!")

    def generate_patient_lesion_report(self):
        """
        生成患者病灶定位报告
        """
        print("生成患者病灶定位报告...")

        # 创建病灶热图数据
        channels = self.eeg_channels
        bands = list(self.frequency_bands.keys())

        lesion_heatmap = np.zeros((len(channels), len(bands)))

        for i, channel in enumerate(channels):
            for j, band in enumerate(bands):
                key = (channel, band)
                if key in self.detection_results:
                    lesion_ratio = self.detection_results[key]['lesion_ratio']
                    lesion_heatmap[i, j] = lesion_ratio

        # 绘制病灶热图
        plt.figure(figsize=(12, 8))

        sns.heatmap(lesion_heatmap,
                   xticklabels=bands,
                   yticklabels=channels,
                   annot=True,
                   fmt='.2f',
                   cmap='Reds',
                   cbar_kws={'label': '病灶比例'})

        plt.title('癫痫患者病灶定位热图\n(颜色越深表示该通道-频段的病灶比例越高)',
                 fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('频率波段', fontsize=12)
        plt.ylabel('EEG通道', fontsize=12)

        plt.tight_layout()
        plt.savefig('患者病灶定位热图.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 生成详细报告
        self.generate_detailed_lesion_report(lesion_heatmap, channels, bands)

        print("患者病灶定位报告生成完成!")

    def generate_detailed_lesion_report(self, lesion_heatmap, channels, bands):
        """
        生成详细的病灶分析报告
        """
        # 找出最严重的病灶区域
        max_lesion_ratio = np.max(lesion_heatmap)
        max_indices = np.where(lesion_heatmap == max_lesion_ratio)

        if len(max_indices[0]) > 0:
            max_channel = channels[max_indices[0][0]]
            max_band = bands[max_indices[1][0]]
        else:
            max_channel, max_band = "未检测到", "未检测到"

        # 统计每个通道的病灶情况
        channel_lesion_scores = np.mean(lesion_heatmap, axis=1)
        band_lesion_scores = np.mean(lesion_heatmap, axis=0)

        # 生成报告
        report = {
            "患者病灶分析报告": {
                "最严重病灶位置": {
                    "通道": max_channel,
                    "频率波段": max_band,
                    "病灶比例": float(max_lesion_ratio)
                },
                "通道病灶排名": [
                    {"通道": channels[i], "平均病灶比例": float(score)}
                    for i, score in sorted(enumerate(channel_lesion_scores),
                                         key=lambda x: x[1], reverse=True)
                ],
                "频率波段病灶排名": [
                    {"频率波段": bands[i], "平均病灶比例": float(score)}
                    for i, score in sorted(enumerate(band_lesion_scores),
                                         key=lambda x: x[1], reverse=True)
                ],
                "总体统计": {
                    "检测的通道-波段组合数": len(self.detection_results),
                    "平均病灶比例": float(np.mean(lesion_heatmap)),
                    "最大病灶比例": float(max_lesion_ratio),
                    "病灶检出率": f"{np.sum(lesion_heatmap > 0.1) / lesion_heatmap.size * 100:.1f}%"
                }
            }
        }

        # 保存报告
        os.makedirs('comprehensive_lesion_results', exist_ok=True)
        with open('comprehensive_lesion_results/患者病灶分析报告.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        # 打印关键发现
        print("\n🎯 关键发现:")
        print(f"- 最严重病灶位置: {max_channel} 通道的 {max_band} 波段 (病灶比例: {max_lesion_ratio:.2f})")
        print(f"- 最易受影响的通道: {report['患者病灶分析报告']['通道病灶排名'][0]['通道']}")
        print(f"- 最易受影响的频率波段: {report['患者病灶分析报告']['频率波段病灶排名'][0]['频率波段']}")
        print(f"- 总体病灶检出率: {report['患者病灶分析报告']['总体统计']['病灶检出率']}")


def main():
    """
    主函数 - 全面病灶检测管道
    """
    print("=" * 80)
    print("全面病灶检测管道 - 多通道多波段分析")
    print("=" * 80)
    
    # 初始化检测器
    detector = ComprehensiveLesionDetector()
    
    # 步骤1: 加载真实标签数据
    print("\n步骤1: 加载真实标签的控制组和癫痫组数据")
    if not detector.load_real_labeled_data():
        print("❌ 数据加载失败，退出程序")
        return
    
    print(f"\n数据加载完成，共有 {len(detector.control_data)} 个通道-波段组合")

    # 步骤2: 训练所有模型 (每个200轮)
    print("\n步骤2: 训练所有通道-波段模型 (每个200轮)")
    detector.train_all_models(epochs=200)

    # 步骤3: 全面病灶检测
    print("\n步骤3: 全面病灶检测")
    detector.detect_lesions_comprehensive()

    # 步骤4: 生成患者病灶定位报告
    print("\n步骤4: 生成患者病灶定位报告")
    detector.generate_patient_lesion_report()

    # 步骤5: 保存所有结果
    print("\n步骤5: 保存所有结果")
    save_comprehensive_results(detector)

    print("\n" + "=" * 80)
    print("全面病灶检测管道完成!")
    print("=" * 80)
    print("主要成果:")
    print(f"- ✅ 训练了 {len(detector.models)} 个V2自动编码器模型")
    print(f"- ✅ 检测了 {len(detector.detection_results)} 个通道-波段组合")
    print("- ✅ 生成了患者病灶定位热图")
    print("- ✅ 生成了详细的病灶分析报告")
    print("- ✅ 识别了每个患者的具体病灶位置")

    return detector


def save_comprehensive_results(detector):
    """
    保存全面检测结果
    """
    print("保存全面检测结果...")

    os.makedirs('comprehensive_lesion_results', exist_ok=True)

    # 保存所有模型
    models_dir = 'comprehensive_lesion_results/models'
    os.makedirs(models_dir, exist_ok=True)

    for key, model in detector.models.items():
        channel_name, band_name = key
        model_filename = f'{channel_name}_{band_name}_autoencoder_v2.keras'
        model.save(os.path.join(models_dir, model_filename))

    # 保存所有标准化器
    scalers_dir = 'comprehensive_lesion_results/scalers'
    os.makedirs(scalers_dir, exist_ok=True)

    import joblib
    for key, scaler in detector.scalers.items():
        channel_name, band_name = key
        scaler_filename = f'{channel_name}_{band_name}_scaler.pkl'
        joblib.dump(scaler, os.path.join(scalers_dir, scaler_filename))

    # 保存检测结果
    results_dir = 'comprehensive_lesion_results/detection_results'
    os.makedirs(results_dir, exist_ok=True)

    for key, results in detector.detection_results.items():
        channel_name, band_name = key

        # 保存重建误差
        np.save(os.path.join(results_dir, f'{channel_name}_{band_name}_reconstruction_errors.npy'),
                results['reconstruction_errors'])

        # 保存病灶掩码
        np.save(os.path.join(results_dir, f'{channel_name}_{band_name}_lesion_mask.npy'),
                results['lesion_mask'])

        # 保存病灶索引
        np.save(os.path.join(results_dir, f'{channel_name}_{band_name}_lesion_indices.npy'),
                results['lesion_indices'])

    # 保存汇总统计
    summary_stats = {
        'total_models_trained': len(detector.models),
        'total_combinations_detected': len(detector.detection_results),
        'channels_analyzed': detector.eeg_channels,
        'frequency_bands_analyzed': list(detector.frequency_bands.keys()),
        'training_epochs_per_model': 200,
        'model_architecture': 'V2_autoencoder_with_skip_connections'
    }

    with open('comprehensive_lesion_results/检测汇总统计.json', 'w', encoding='utf-8') as f:
        json.dump(summary_stats, f, indent=2, ensure_ascii=False)

    print("全面检测结果保存完成!")
    print("保存的文件:")
    print(f"- {len(detector.models)} 个训练好的模型")
    print(f"- {len(detector.scalers)} 个数据标准化器")
    print(f"- {len(detector.detection_results)} 个检测结果文件")
    print("- 患者病灶分析报告.json")
    print("- 患者病灶定位热图.png")
    print("- 检测汇总统计.json")


if __name__ == "__main__":
    detector = main()
