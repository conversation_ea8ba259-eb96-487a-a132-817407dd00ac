#!/usr/bin/env python3
"""
改进的癫痫自动编码器管道
Improved Epilepsy Autoencoder Pipeline

主要改进:
1. 更好的数据预处理策略
2. 优化的模型架构
3. 中文图表和输出
4. 更稳定的训练策略
5. 详细的性能分析
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import warnings
import os
from pathlib import Path
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class ImprovedAttentionLayer(layers.Layer):
    """
    改进的注意力层
    """
    def __init__(self, units, **kwargs):
        super(ImprovedAttentionLayer, self).__init__(**kwargs)
        self.units = units
        self.W = layers.Dense(units, activation='tanh')
        self.U = layers.Dense(1, activation='linear')
        
    def call(self, inputs):
        # inputs shape: (batch_size, time_steps, features)
        # 计算注意力分数
        attention_scores = self.U(self.W(inputs))  # (batch_size, time_steps, 1)
        attention_weights = tf.nn.softmax(attention_scores, axis=1)
        
        # 应用注意力权重
        context_vector = tf.reduce_sum(inputs * attention_weights, axis=1)  # (batch_size, features)
        
        return context_vector, attention_weights
    
    def get_config(self):
        config = super().get_config()
        config.update({"units": self.units})
        return config

class ImprovedEpilepsyAutoencoder:
    """
    改进的癫痫自动编码器
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        """
        初始化改进的管道
        
        Parameters:
        -----------
        segment_length : int
            每段的长度 (4秒 = 512样本点)
        overlap_ratio : float
            重叠比例 (增加到75%以获得更多训练数据)
        sampling_rate : int
            采样率
        """
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        # 存储数据
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.history = None
        
        print(f"初始化改进的癫痫自动编码器:")
        print(f"- 分段长度: {segment_length} 样本点 ({segment_length/sampling_rate:.1f}秒)")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 步长: {self.step_size} 样本点")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """
        加载并改进预处理数据
        """
        print("加载Gamma波数据...")
        
        # 加载原始数据
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        print(f"原始数据: {gamma_waves.shape[0]} 个样本, 每个 {gamma_waves.shape[1]} 个时间点")
        
        # 改进的预处理
        processed_waves = []
        
        for i, wave in enumerate(gamma_waves):
            # 1. 去除异常值 (使用3σ原则)
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            # 2. 去趋势 (去除线性趋势)
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            # 3. 带通滤波 (保留Gamma频段)
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist  # 稍微放宽频带
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            # 4. Z-score标准化 (每个样本独立标准化)
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            
            processed_waves.append(wave_normalized)
        
        processed_waves = np.array(processed_waves)
        
        print(f"预处理完成:")
        print(f"- 原始范围: [{gamma_waves.min():.2f}, {gamma_waves.max():.2f}]")
        print(f"- 处理后范围: [{processed_waves.min():.2f}, {processed_waves.max():.2f}]")
        
        return processed_waves, metadata
    
    def create_segments(self, processed_waves):
        """
        创建改进的数据段
        """
        print("创建数据段...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            # 计算段数 (使用更小的步长获得更多数据)
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    # 检查段的质量 (去除全零或异常段)
                    if np.std(segment) > 0.01:  # 确保有足够的变化
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        print(f"段创建完成: {len(segments)} 个有效段")
        
        # 全局标准化 (在所有段上)
        self.scaler = RobustScaler()  # 使用RobustScaler，对异常值更鲁棒
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        segments_final = segments_scaled.reshape(segments.shape)
        
        print(f"最终数据范围: [{segments_final.min():.3f}, {segments_final.max():.3f}]")
        
        self.segments = segments_final
        self.segment_info = segment_info
        
        return segments_final, segment_info
    
    def build_improved_autoencoder(self, input_dim=512, encoding_dim=32, 
                                 lstm_units=64, attention_units=32):
        """
        构建改进的自动编码器架构
        """
        print("构建改进的自动编码器...")
        
        # ==================== 编码器 ====================
        input_layer = keras.Input(shape=(input_dim,), name='input_layer')

        # 重塑为时间序列 (更合理的时间步划分)
        time_steps = 32  # 减少时间步数，增加每步特征数
        features_per_step = input_dim // time_steps

        reshaped = layers.Reshape((time_steps, features_per_step), name='reshape_layer')(input_layer)

        # 第一层Bi-LSTM (较小的单元数)
        bilstm1 = layers.Bidirectional(
            layers.LSTM(lstm_units, return_sequences=True, dropout=0.1, recurrent_dropout=0.1),
            name='bilstm1'
        )(reshaped)

        # 批标准化
        bn1 = layers.BatchNormalization(name='bn1')(bilstm1)

        # 第二层Bi-LSTM
        bilstm2 = layers.Bidirectional(
            layers.LSTM(lstm_units//2, return_sequences=True, dropout=0.1, recurrent_dropout=0.1),
            name='bilstm2'
        )(bn1)

        # 注意力机制
        attention_layer = ImprovedAttentionLayer(attention_units, name='attention_layer')
        context_vector, attention_weights = attention_layer(bilstm2)

        # 编码层
        encoded = layers.Dense(encoding_dim, activation='relu', name='encoding_layer')(context_vector)
        encoded = layers.BatchNormalization(name='encoding_bn')(encoded)
        encoded = layers.Dropout(0.2, name='encoding_dropout')(encoded)
        
        # ==================== 解码器 ====================
        # 解码开始
        decoded = layers.Dense(lstm_units, activation='relu', name='decode_start')(encoded)
        decoded = layers.BatchNormalization(name='decode_bn')(decoded)
        decoded = layers.Dropout(0.2, name='decode_dropout')(decoded)

        # 重复向量
        decoded = layers.RepeatVector(time_steps, name='repeat_vector')(decoded)

        # 解码LSTM层
        decode_lstm1 = layers.LSTM(lstm_units, return_sequences=True, dropout=0.1,
                                 recurrent_dropout=0.1, name='decode_lstm1')(decoded)

        decode_lstm2 = layers.LSTM(lstm_units//2, return_sequences=True, dropout=0.1,
                                 recurrent_dropout=0.1, name='decode_lstm2')(decode_lstm1)

        # 时间分布式输出
        time_distributed = layers.TimeDistributed(
            layers.Dense(features_per_step, activation='linear'),
            name='time_distributed'
        )(decode_lstm2)

        # 重塑回原始维度
        output = layers.Reshape((input_dim,), name='output_reshape')(time_distributed)

        # ==================== 构建模型 ====================
        autoencoder = keras.Model(input_layer, output, name='improved_autoencoder')
        encoder = keras.Model(input_layer, [encoded, attention_weights], name='improved_encoder')
        
        # 使用更保守的学习率和优化器
        optimizer = keras.optimizers.Adam(learning_rate=0.0005, beta_1=0.9, beta_2=0.999)
        
        autoencoder.compile(
            optimizer=optimizer,
            loss='huber',  # 使用Huber损失，对异常值更鲁棒
            metrics=['mae', 'mse']
        )
        
        print("改进模型构建完成:")
        print(f"- 输入维度: {input_dim}")
        print(f"- 时间步数: {time_steps}")
        print(f"- 每步特征数: {features_per_step}")
        print(f"- LSTM单元数: {lstm_units}")
        print(f"- 编码维度: {encoding_dim}")
        print(f"- 总参数: {autoencoder.count_params():,}")
        
        self.autoencoder = autoencoder
        self.encoder = encoder
        
        return autoencoder, encoder
    
    def train_improved_model(self, X_train, X_val=None, epochs=100, batch_size=32):
        """
        改进的训练策略
        """
        print("开始训练改进模型...")
        
        # 改进的回调函数
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=20,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=10,
                min_lr=1e-6,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'best_improved_autoencoder.keras',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]
        
        # 训练
        if X_val is not None:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_data=(X_val, X_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_split=0.2,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        
        self.history = history
        print("改进模型训练完成!")
        
        return history
    
    def comprehensive_evaluation(self, X_test):
        """
        全面评估模型性能
        """
        print("进行全面性能评估...")
        
        # 重建预测
        X_reconstructed = self.autoencoder.predict(X_test, verbose=0)
        
        # 计算多种误差指标
        mse_errors = np.mean((X_test - X_reconstructed) ** 2, axis=1)
        mae_errors = np.mean(np.abs(X_test - X_reconstructed), axis=1)
        
        # 相关系数
        correlations = []
        for i in range(len(X_test)):
            corr = np.corrcoef(X_test[i], X_reconstructed[i])[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        correlations = np.array(correlations)
        
        print(f"性能评估结果:")
        print(f"- MSE误差: 均值={np.mean(mse_errors):.4f}, 标准差={np.std(mse_errors):.4f}")
        print(f"- MAE误差: 均值={np.mean(mae_errors):.4f}, 标准差={np.std(mae_errors):.4f}")
        print(f"- 相关系数: 均值={np.mean(correlations):.4f}, 标准差={np.std(correlations):.4f}")
        print(f"- 最佳重建相关系数: {np.max(correlations):.4f}")
        print(f"- 最差重建相关系数: {np.min(correlations):.4f}")
        
        return {
            'mse_errors': mse_errors,
            'mae_errors': mae_errors,
            'correlations': correlations,
            'reconstructed': X_reconstructed
        }


def main():
    """
    主函数 - 执行改进的训练管道
    """
    print("=" * 60)
    print("改进的癫痫自动编码器训练管道")
    print("=" * 60)
    
    # 初始化改进管道
    pipeline = ImprovedEpilepsyAutoencoder(
        segment_length=512,
        overlap_ratio=0.75,  # 增加重叠以获得更多数据
        sampling_rate=128
    )
    
    # 步骤1: 加载和预处理数据
    print("\n步骤1: 加载和改进预处理")
    processed_waves, metadata = pipeline.load_and_preprocess_data()
    
    # 步骤2: 创建数据段
    print("\n步骤2: 创建改进数据段")
    segments, segment_info = pipeline.create_segments(processed_waves)
    
    # 步骤3: 划分数据集
    print("\n步骤3: 划分数据集")
    X_train, X_test = train_test_split(segments, test_size=0.2, random_state=42)
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")
    
    # 步骤4: 构建改进模型
    print("\n步骤4: 构建改进自动编码器")
    autoencoder, encoder = pipeline.build_improved_autoencoder(
        input_dim=512,
        encoding_dim=32,
        lstm_units=64,
        attention_units=32
    )
    
    print("\n改进模型结构:")
    autoencoder.summary()

    # 步骤5: 训练改进模型
    print("\n步骤5: 训练改进模型")
    history = pipeline.train_improved_model(
        X_train,
        epochs=80,
        batch_size=32
    )

    # 步骤6: 全面评估
    print("\n步骤6: 全面性能评估")
    evaluation_results = pipeline.comprehensive_evaluation(X_test)

    # 步骤7: 中文可视化
    print("\n步骤7: 创建中文可视化")
    create_chinese_visualizations(pipeline, X_test, evaluation_results)

    # 步骤8: 保存改进模型
    print("\n步骤8: 保存改进模型")
    os.makedirs('improved_autoencoder_models', exist_ok=True)
    pipeline.autoencoder.save('improved_autoencoder_models/improved_autoencoder.keras')
    pipeline.encoder.save('improved_autoencoder_models/improved_encoder.keras')

    import joblib
    joblib.dump(pipeline.scaler, 'improved_autoencoder_models/improved_scaler.pkl')

    print("\n改进模型训练完成!")
    print("主要改进:")
    print("- ✅ 更好的数据预处理 (去趋势、滤波、异常值处理)")
    print("- ✅ 优化的模型架构 (更小更稳定)")
    print("- ✅ 改进的训练策略 (Huber损失、批标准化)")
    print("- ✅ 中文图表和输出")
    print("- ✅ 全面的性能评估")

    return pipeline, X_train, X_test, evaluation_results


def create_chinese_visualizations(pipeline, X_test, evaluation_results):
    """
    创建中文可视化图表
    """
    print("创建中文可视化图表...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 训练历史可视化
    if pipeline.history is not None:
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 损失曲线
        axes[0, 0].plot(pipeline.history.history['loss'], 'b-', label='训练损失', linewidth=2)
        if 'val_loss' in pipeline.history.history:
            axes[0, 0].plot(pipeline.history.history['val_loss'], 'r-', label='验证损失', linewidth=2)
        axes[0, 0].set_title('模型损失曲线', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('训练轮数')
        axes[0, 0].set_ylabel('损失值')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)

        # MAE曲线
        axes[0, 1].plot(pipeline.history.history['mae'], 'g-', label='训练MAE', linewidth=2)
        if 'val_mae' in pipeline.history.history:
            axes[0, 1].plot(pipeline.history.history['val_mae'], 'orange', label='验证MAE', linewidth=2)
        axes[0, 1].set_title('平均绝对误差曲线', fontsize=14, fontweight='bold')
        axes[0, 1].set_xlabel('训练轮数')
        axes[0, 1].set_ylabel('MAE值')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)

        # MSE曲线
        axes[1, 0].plot(pipeline.history.history['mse'], 'purple', label='训练MSE', linewidth=2)
        if 'val_mse' in pipeline.history.history:
            axes[1, 0].plot(pipeline.history.history['val_mse'], 'brown', label='验证MSE', linewidth=2)
        axes[1, 0].set_title('均方误差曲线', fontsize=14, fontweight='bold')
        axes[1, 0].set_xlabel('训练轮数')
        axes[1, 0].set_ylabel('MSE值')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)

        # 学习率曲线
        if 'lr' in pipeline.history.history:
            axes[1, 1].plot(pipeline.history.history['lr'], 'red', label='学习率', linewidth=2)
            axes[1, 1].set_title('学习率变化', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('训练轮数')
            axes[1, 1].set_ylabel('学习率')
            axes[1, 1].set_yscale('log')
            axes[1, 1].legend()
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, '学习率数据不可用', ha='center', va='center',
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].set_title('学习率变化', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig('改进模型训练历史.png', dpi=300, bbox_inches='tight')
        plt.show()

    # 2. 重建效果对比
    fig, axes = plt.subplots(3, 1, figsize=(15, 12))

    time = np.arange(512) / 128  # 4秒时间轴
    X_reconstructed = evaluation_results['reconstructed']
    correlations = evaluation_results['correlations']

    # 选择最好、中等、最差的重建样本
    best_idx = np.argmax(correlations)
    worst_idx = np.argmin(correlations)
    median_idx = np.argsort(correlations)[len(correlations)//2]

    samples = [
        (best_idx, f'最佳重建 (相关系数: {correlations[best_idx]:.3f})', 'green'),
        (median_idx, f'中等重建 (相关系数: {correlations[median_idx]:.3f})', 'blue'),
        (worst_idx, f'最差重建 (相关系数: {correlations[worst_idx]:.3f})', 'red')
    ]

    for i, (idx, title, color) in enumerate(samples):
        axes[i].plot(time, X_test[idx], 'k-', label='原始信号', linewidth=2, alpha=0.8)
        axes[i].plot(time, X_reconstructed[idx], '--', color=color, label='重建信号', linewidth=2)
        axes[i].set_title(title, fontsize=12, fontweight='bold')
        axes[i].set_xlabel('时间 (秒)')
        axes[i].set_ylabel('标准化幅值')
        axes[i].legend()
        axes[i].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('改进模型重建对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 性能分析图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    # MSE误差分布
    axes[0, 0].hist(evaluation_results['mse_errors'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('MSE误差分布', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('MSE误差')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].grid(True, alpha=0.3)

    # MAE误差分布
    axes[0, 1].hist(evaluation_results['mae_errors'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 1].set_title('MAE误差分布', fontsize=12, fontweight='bold')
    axes[0, 1].set_xlabel('MAE误差')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].grid(True, alpha=0.3)

    # 相关系数分布
    axes[1, 0].hist(correlations, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1, 0].set_title('重建相关系数分布', fontsize=12, fontweight='bold')
    axes[1, 0].set_xlabel('相关系数')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].grid(True, alpha=0.3)

    # 误差vs相关系数散点图
    axes[1, 1].scatter(correlations, evaluation_results['mse_errors'], alpha=0.6, color='purple')
    axes[1, 1].set_title('相关系数 vs MSE误差', fontsize=12, fontweight='bold')
    axes[1, 1].set_xlabel('相关系数')
    axes[1, 1].set_ylabel('MSE误差')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('改进模型性能分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("中文可视化图表创建完成!")


if __name__ == "__main__":
    pipeline, X_train, X_test, evaluation_results = main()
