#!/usr/bin/env python3
"""
Epilepsy Wavelet Feature Extractor
Extract important decomposed waves from key epilepsy channels to create a new dataset

This script identifies the most important epilepsy-related channels and extracts
specific wavelet decomposition components that are clinically significant for
epilepsy detection and analysis.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import pywt
import gzip
import os
from scipy import signal
from scipy.stats import entropy, skew, kurtosis
import warnings
from pathlib import Path

warnings.filterwarnings('ignore')

class EpilepsyWaveletExtractor:
    """
    Extract important wavelet components from epilepsy EEG data
    """
    
    def __init__(self, sfreq=128):
        """
        Initialize the extractor
        
        Parameters:
        -----------
        sfreq : float
            Sampling frequency in Hz
        """
        self.sfreq = sfreq
        
        # Standard 10-20 electrode positions
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
        # Clinical frequency bands
        self.frequency_bands = {
            'Delta': (0.5, 4),
            'Theta': (4, 8),
            'Alpha': (8, 13),
            'Beta': (13, 30),
            'Gamma': (30, 60)
        }
        
        # Key epilepsy channels (temporal and frontal regions)
        self.epilepsy_key_channels = ['T7', 'T8', 'F7', 'F8', 'FC5', 'FC6']
        
    def load_eeg_data(self, file_path):
        """
        Load EEG data from compressed CSV file

        Parameters:
        -----------
        file_path : str
            Path to the EEG data file

        Returns:
        --------
        data : numpy.ndarray
            EEG data matrix (channels x time)
        """
        try:
            with gzip.open(file_path, 'rt') as f:
                data = pd.read_csv(f, header=0, low_memory=False)  # Read with header

            # Remove the first column (index) and extract EEG channels
            # The EEG channels are typically columns 1-14 (AF3, AF4, F3, F4, etc.)
            eeg_columns = []
            for col in data.columns[1:15]:  # Skip first column, take next 14
                if col in self.eeg_channels or any(ch in str(col) for ch in self.eeg_channels):
                    eeg_columns.append(col)

            if not eeg_columns:
                # Fallback: take columns 1-14 regardless of names
                eeg_columns = data.columns[1:15]

            # Extract numeric data only (skip header row if it exists)
            eeg_data_df = data[eeg_columns].iloc[1:]  # Skip first row which might be header

            # Convert to numeric, replacing non-numeric values with NaN
            eeg_data_numeric = eeg_data_df.apply(pd.to_numeric, errors='coerce')

            # Drop rows with NaN values
            eeg_data_clean = eeg_data_numeric.dropna()

            if eeg_data_clean.empty:
                print(f"No valid numeric data found in {file_path}")
                return None

            # Convert to numpy array and transpose (channels x time)
            eeg_data = eeg_data_clean.values.T

            # Ensure we have the expected number of channels
            if eeg_data.shape[0] > len(self.eeg_channels):
                print(f"Taking first {len(self.eeg_channels)} channels from {eeg_data.shape[0]} available")
                eeg_data = eeg_data[:len(self.eeg_channels), :]

            print(f"Loaded EEG data shape: {eeg_data.shape}")
            return eeg_data

        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return None
    
    def preprocess_signal(self, signal_data, channel_idx=0):
        """
        Basic preprocessing of EEG signal
        
        Parameters:
        -----------
        signal_data : numpy.ndarray
            Raw EEG signal
        channel_idx : int
            Channel index to process
            
        Returns:
        --------
        processed_signal : numpy.ndarray
            Preprocessed signal
        """
        if signal_data is None or signal_data.shape[0] <= channel_idx:
            return None
        
        # Extract single channel
        eeg_signal = signal_data[channel_idx, :]
        
        # Remove DC component
        eeg_signal = eeg_signal - np.mean(eeg_signal)
        
        # Apply bandpass filter (0.5-60 Hz)
        nyquist = self.sfreq / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist
        
        # Design Butterworth filter
        b, a = signal.butter(4, [low_freq, high_freq], btype='band')
        filtered_signal = signal.filtfilt(b, a, eeg_signal)
        
        return filtered_signal
    
    def wavelet_decomposition(self, eeg_signal, wavelet='db4', levels=8):
        """
        Perform multi-level wavelet decomposition
        
        Parameters:
        -----------
        eeg_signal : numpy.ndarray
            Preprocessed EEG signal
        wavelet : str
            Wavelet function (Daubechies 4 for clinical EEG)
        levels : int
            Number of decomposition levels
            
        Returns:
        --------
        coeffs : list
            Wavelet coefficients [cA_n, cD_n, cD_n-1, ..., cD_1]
        """
        coeffs = pywt.wavedec(eeg_signal, wavelet, level=levels)
        return coeffs
    
    def extract_frequency_bands(self, coeffs, wavelet='db4'):
        """
        Extract clinical frequency bands from wavelet coefficients
        
        Parameters:
        -----------
        coeffs : list
            Wavelet coefficients
        wavelet : str
            Wavelet function used
            
        Returns:
        --------
        bands : dict
            Dictionary containing reconstructed signals for each frequency band
        """
        bands = {}
        
        # Gamma (30-60 Hz) - level 1
        gamma_coeffs = [np.zeros_like(coeffs[0])] + [coeffs[1]] + [np.zeros_like(c) for c in coeffs[2:]]
        bands['Gamma'] = pywt.waverec(gamma_coeffs, wavelet)
        
        # Beta (13-30 Hz) - level 2
        beta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(coeffs[1])] + [coeffs[2]] + [np.zeros_like(c) for c in coeffs[3:]]
        bands['Beta'] = pywt.waverec(beta_coeffs, wavelet)
        
        # Alpha (8-13 Hz) - level 3
        alpha_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:3]] + [coeffs[3]] + [np.zeros_like(c) for c in coeffs[4:]]
        bands['Alpha'] = pywt.waverec(alpha_coeffs, wavelet)
        
        # Theta (4-8 Hz) - level 4
        theta_coeffs = [np.zeros_like(coeffs[0])] + [np.zeros_like(c) for c in coeffs[1:4]] + [coeffs[4]] + [np.zeros_like(c) for c in coeffs[5:]]
        bands['Theta'] = pywt.waverec(theta_coeffs, wavelet)
        
        # Delta (0.5-4 Hz) - levels 5-8 and approximation
        delta_coeffs = [coeffs[0]] + [np.zeros_like(c) for c in coeffs[1:5]] + coeffs[5:]
        bands['Delta'] = pywt.waverec(delta_coeffs, wavelet)
        
        return bands
    
    def calculate_epilepsy_features(self, band_signal):
        """
        Calculate epilepsy-relevant features from frequency band signal
        
        Parameters:
        -----------
        band_signal : numpy.ndarray
            Frequency band signal
            
        Returns:
        --------
        features : dict
            Dictionary of calculated features
        """
        features = {}
        
        # Statistical features
        features['mean'] = np.mean(band_signal)
        features['std'] = np.std(band_signal)
        features['variance'] = np.var(band_signal)
        features['skewness'] = skew(band_signal)
        features['kurtosis'] = kurtosis(band_signal)
        
        # Power features
        features['rms'] = np.sqrt(np.mean(band_signal**2))
        features['peak_to_peak'] = np.ptp(band_signal)
        
        # Spectral features
        freqs, psd = signal.welch(band_signal, fs=self.sfreq, nperseg=256)
        features['spectral_centroid'] = np.sum(freqs * psd) / np.sum(psd)
        features['spectral_entropy'] = entropy(psd + 1e-12)  # Add small value to avoid log(0)
        
        # Complexity features
        features['zero_crossings'] = np.sum(np.diff(np.sign(band_signal)) != 0)
        
        return features
    
    def identify_key_epilepsy_channel(self, eeg_data):
        """
        Identify the most important epilepsy channel based on gamma activity

        Parameters:
        -----------
        eeg_data : numpy.ndarray
            Multi-channel EEG data

        Returns:
        --------
        key_channel_idx : int
            Index of the most important channel
        key_channel_name : str
            Name of the most important channel
        """
        gamma_powers = []

        # Check all available channels
        for ch_idx in range(min(eeg_data.shape[0], len(self.eeg_channels))):
            ch_name = self.eeg_channels[ch_idx] if ch_idx < len(self.eeg_channels) else f'CH_{ch_idx}'

            # Preprocess signal
            processed_signal = self.preprocess_signal(eeg_data, ch_idx)
            if processed_signal is not None:
                try:
                    # Decompose and extract gamma
                    coeffs = self.wavelet_decomposition(processed_signal)
                    bands = self.extract_frequency_bands(coeffs)

                    # Calculate gamma power
                    gamma_power = np.mean(bands['Gamma']**2)

                    # Give higher weight to epilepsy-relevant channels
                    weight = 2.0 if ch_name in self.epilepsy_key_channels else 1.0
                    weighted_power = gamma_power * weight

                    gamma_powers.append((ch_idx, ch_name, weighted_power))

                except Exception as e:
                    print(f"Error processing channel {ch_idx} ({ch_name}): {e}")
                    continue

        if gamma_powers:
            # Sort by weighted gamma power and return the highest
            gamma_powers.sort(key=lambda x: x[2], reverse=True)
            key_channel_idx, key_channel_name, _ = gamma_powers[0]
            print(f"Selected key channel: {key_channel_name} (index {key_channel_idx})")
            return key_channel_idx, key_channel_name
        else:
            # Default to first available channel
            print("Using default channel 0")
            return 0, self.eeg_channels[0] if self.eeg_channels else 'CH_0'
    
    def extract_epilepsy_dataset(self, data_dir, output_dir, max_samples=50):
        """
        Extract epilepsy wavelet dataset from EEG files
        
        Parameters:
        -----------
        data_dir : str
            Directory containing EEG data files
        output_dir : str
            Directory to save the extracted dataset
        max_samples : int
            Maximum number of samples to process
        """
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Load metadata
        metadata_file = os.path.join(data_dir, 'metadata_nigeria.csv')
        if os.path.exists(metadata_file):
            metadata = pd.read_csv(metadata_file)
            epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        else:
            # Fallback: look for epilepsy files by pattern
            epilepsy_files = [f for f in os.listdir(os.path.join(data_dir, 'EEGs_Nigeria')) 
                            if f.startswith('signal-5') and f.endswith('.csv.gz')]
        
        dataset = []
        processed_count = 0
        
        print(f"Processing epilepsy files from {data_dir}...")
        
        for file_name in epilepsy_files[:max_samples]:
            file_path = os.path.join(data_dir, 'EEGs_Nigeria', file_name)
            
            if not os.path.exists(file_path):
                continue
                
            print(f"Processing {file_name}...")
            
            # Load EEG data
            eeg_data = self.load_eeg_data(file_path)
            if eeg_data is None:
                continue
            
            # Identify key epilepsy channel
            key_ch_idx, key_ch_name = self.identify_key_epilepsy_channel(eeg_data)
            
            # Process the key channel
            processed_signal = self.preprocess_signal(eeg_data, key_ch_idx)
            if processed_signal is None:
                continue
            
            # Wavelet decomposition
            coeffs = self.wavelet_decomposition(processed_signal)
            bands = self.extract_frequency_bands(coeffs)
            
            # Extract features for each frequency band
            sample_data = {
                'file_name': file_name,
                'key_channel': key_ch_name,
                'key_channel_idx': key_ch_idx
            }
            
            # Store the most important decomposed wave (Gamma band for epilepsy)
            gamma_wave = bands['Gamma']
            sample_data['gamma_wave'] = gamma_wave
            
            # Calculate features for all bands
            for band_name, band_signal in bands.items():
                features = self.calculate_epilepsy_features(band_signal)
                for feature_name, feature_value in features.items():
                    sample_data[f'{band_name}_{feature_name}'] = feature_value
            
            dataset.append(sample_data)
            processed_count += 1
            
            if processed_count >= max_samples:
                break
        
        print(f"Processed {processed_count} epilepsy samples")
        
        # Save the dataset
        self.save_dataset(dataset, output_dir)
        
        return dataset
    
    def save_dataset(self, dataset, output_dir):
        """
        Save the extracted dataset

        Parameters:
        -----------
        dataset : list
            List of sample dictionaries
        output_dir : str
            Output directory
        """
        if not dataset:
            print("No data to save")
            return

        # Create feature matrix (excluding gamma_wave which is the signal itself)
        feature_data = []
        gamma_waves = []

        # Find the minimum length for consistent array size
        min_length = min(len(sample['gamma_wave']) for sample in dataset)
        print(f"Truncating all gamma waves to length: {min_length}")

        for sample in dataset:
            # Extract features (exclude non-numeric fields)
            features = {}
            for key, value in sample.items():
                if key not in ['file_name', 'key_channel', 'gamma_wave'] and isinstance(value, (int, float)):
                    features[key] = value

            feature_data.append(features)

            # Truncate gamma wave to consistent length
            gamma_wave_truncated = sample['gamma_wave'][:min_length]
            gamma_waves.append(gamma_wave_truncated)

        # Save feature matrix
        features_df = pd.DataFrame(feature_data)
        features_df.to_csv(os.path.join(output_dir, 'epilepsy_wavelet_features.csv'), index=False)

        # Save gamma waves (most important decomposed waves)
        gamma_waves_array = np.array(gamma_waves)
        np.save(os.path.join(output_dir, 'epilepsy_gamma_waves.npy'), gamma_waves_array)
        
        # Save metadata
        metadata = []
        for sample in dataset:
            metadata.append({
                'file_name': sample['file_name'],
                'key_channel': sample['key_channel'],
                'key_channel_idx': sample['key_channel_idx']
            })
        
        metadata_df = pd.DataFrame(metadata)
        metadata_df.to_csv(os.path.join(output_dir, 'epilepsy_metadata.csv'), index=False)
        
        print(f"Dataset saved to {output_dir}")
        print(f"- Features: {features_df.shape}")
        print(f"- Gamma waves: {gamma_waves_array.shape}")
        print(f"- Metadata: {metadata_df.shape}")


def main():
    """
    Main function to extract epilepsy wavelet dataset
    """
    # Initialize extractor
    extractor = EpilepsyWaveletExtractor()
    
    # Set paths
    data_dir = '1252141'
    output_dir = 'epilepsy_wavelet_dataset'
    
    # Extract dataset
    dataset = extractor.extract_epilepsy_dataset(data_dir, output_dir, max_samples=30)
    
    print(f"\nExtracted {len(dataset)} epilepsy samples with important decomposed waves")
    print("Dataset includes:")
    print("- Gamma band waves (most important for epilepsy)")
    print("- Multi-band wavelet features")
    print("- Key channel identification")


if __name__ == "__main__":
    main()
