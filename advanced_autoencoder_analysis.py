#!/usr/bin/env python3
"""
Advanced Autoencoder Analysis for Epilepsy Detection
高级自动编码器分析用于癫痫检测

This script provides advanced analysis capabilities:
1. 异常检测 (Anomaly Detection)
2. 潜在空间分析 (Latent Space Analysis)
3. 重建误差分析 (Reconstruction Error Analysis)
4. 特征提取 (Feature Extraction)
5. 可视化分析 (Visualization Analysis)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.manifold import TSNE
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import tensorflow as tf
from tensorflow import keras
import joblib
import os
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

class AdvancedAutoencoderAnalysis:
    """
    高级自动编码器分析类
    """
    
    def __init__(self, model_dir='autoencoder_models'):
        """
        初始化分析器
        
        Parameters:
        -----------
        model_dir : str
            模型目录路径
        """
        self.model_dir = model_dir
        self.autoencoder = None
        self.encoder = None
        self.scaler = None
        self.segments = None
        self.encoded_features = None
        self.reconstruction_errors = None
        
        # 加载模型和缩放器
        self.load_models()
    
    def load_models(self):
        """
        加载训练好的模型和缩放器
        """
        print("加载训练好的模型...")

        # 加载自动编码器
        autoencoder_path = os.path.join(self.model_dir, 'autoencoder.h5')
        encoder_path = os.path.join(self.model_dir, 'encoder.h5')
        scaler_path = os.path.join(self.model_dir, 'scaler.pkl')

        try:
            if os.path.exists(autoencoder_path):
                self.autoencoder = keras.models.load_model(autoencoder_path, compile=False)
                # 重新编译模型
                self.autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])
                print("✓ 自动编码器加载成功")
        except Exception as e:
            print(f"⚠ 自动编码器加载失败: {e}")
            print("将重新构建模型...")
            self.rebuild_models()

        try:
            if os.path.exists(encoder_path):
                self.encoder = keras.models.load_model(encoder_path, compile=False)
                print("✓ 编码器加载成功")
        except Exception as e:
            print(f"⚠ 编码器加载失败: {e}")

        if os.path.exists(scaler_path):
            self.scaler = joblib.load(scaler_path)
            print("✓ 缩放器加载成功")

    def rebuild_models(self):
        """
        重新构建模型 (如果加载失败)
        """
        print("重新构建自动编码器模型...")

        # 重新构建自动编码器
        input_dim = 256
        encoding_dim = 32
        hidden_dims = [128, 64]

        # 输入层
        input_layer = keras.Input(shape=(input_dim,))

        # 编码器部分
        encoded = input_layer
        for dim in hidden_dims:
            encoded = keras.layers.Dense(dim, activation='relu')(encoded)
            encoded = keras.layers.Dropout(0.2)(encoded)

        # 潜在空间
        encoded = keras.layers.Dense(encoding_dim, activation='relu', name='encoding')(encoded)

        # 解码器部分
        decoded = encoded
        for dim in reversed(hidden_dims):
            decoded = keras.layers.Dense(dim, activation='relu')(decoded)
            decoded = keras.layers.Dropout(0.2)(decoded)

        # 输出层
        decoded = keras.layers.Dense(input_dim, activation='linear')(decoded)

        # 构建模型
        self.autoencoder = keras.Model(input_layer, decoded, name='autoencoder')
        self.encoder = keras.Model(input_layer, encoded, name='encoder')

        # 编译模型
        self.autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])

        # 尝试加载权重
        try:
            self.autoencoder.load_weights(os.path.join(self.model_dir, 'autoencoder.h5'))
            print("✓ 权重加载成功")
        except Exception as e:
            print(f"⚠ 权重加载失败: {e}")
            print("将使用未训练的模型进行分析")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """
        加载并预处理数据
        
        Parameters:
        -----------
        dataset_path : str
            数据集路径
        """
        print("加载并预处理数据...")
        
        # 重新创建分段数据 (与训练时相同的处理)
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        
        # 分段参数 (与训练时相同)
        segment_length = 256
        overlap_ratio = 0.5
        step_size = int(segment_length * (1 - overlap_ratio))
        
        # 分割数据
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(gamma_waves):
            n_segments = (len(wave) - segment_length) // step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * step_size
                end_idx = start_idx + segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    all_segments.append(segment)
                    segment_info.append({
                        'sample_idx': sample_idx,
                        'segment_idx': seg_idx,
                        'start_idx': start_idx,
                        'end_idx': end_idx
                    })
        
        segments = np.array(all_segments)
        
        # 预处理 (标准化)
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.transform(segments_reshaped)
        processed_segments = segments_scaled.reshape(segments.shape)
        
        self.segments = processed_segments
        self.segment_info = segment_info
        
        print(f"数据加载完成: {len(processed_segments)} 个段")
        
        return processed_segments, segment_info
    
    def extract_latent_features(self):
        """
        提取潜在空间特征
        
        Returns:
        --------
        encoded_features : numpy.ndarray
            编码后的特征
        """
        if self.segments is None:
            raise ValueError("请先加载数据")
        
        print("提取潜在空间特征...")
        
        # 使用编码器提取特征
        self.encoded_features = self.encoder.predict(self.segments, verbose=0)
        
        print(f"特征提取完成: {self.encoded_features.shape}")
        
        return self.encoded_features
    
    def calculate_reconstruction_errors(self):
        """
        计算重建误差
        
        Returns:
        --------
        reconstruction_errors : numpy.ndarray
            重建误差
        """
        if self.segments is None:
            raise ValueError("请先加载数据")
        
        print("计算重建误差...")
        
        # 重建数据
        reconstructed = self.autoencoder.predict(self.segments, verbose=0)
        
        # 计算重建误差 (MSE)
        self.reconstruction_errors = np.mean((self.segments - reconstructed) ** 2, axis=1)
        
        print(f"重建误差计算完成")
        print(f"- 平均误差: {np.mean(self.reconstruction_errors):.6f}")
        print(f"- 标准差: {np.std(self.reconstruction_errors):.6f}")
        
        return self.reconstruction_errors
    
    def detect_anomalies(self, threshold_method='percentile', threshold_value=95):
        """
        基于重建误差的异常检测
        
        Parameters:
        -----------
        threshold_method : str
            阈值方法 ('percentile', 'std', 'manual')
        threshold_value : float
            阈值参数
            
        Returns:
        --------
        anomalies : numpy.ndarray
            异常标记 (True为异常)
        threshold : float
            使用的阈值
        """
        if self.reconstruction_errors is None:
            self.calculate_reconstruction_errors()
        
        print(f"异常检测 (方法: {threshold_method})...")
        
        if threshold_method == 'percentile':
            threshold = np.percentile(self.reconstruction_errors, threshold_value)
        elif threshold_method == 'std':
            mean_error = np.mean(self.reconstruction_errors)
            std_error = np.std(self.reconstruction_errors)
            threshold = mean_error + threshold_value * std_error
        elif threshold_method == 'manual':
            threshold = threshold_value
        else:
            raise ValueError(f"未知的阈值方法: {threshold_method}")
        
        anomalies = self.reconstruction_errors > threshold
        
        print(f"异常检测完成:")
        print(f"- 阈值: {threshold:.6f}")
        print(f"- 异常样本数: {np.sum(anomalies)} / {len(anomalies)}")
        print(f"- 异常比例: {np.sum(anomalies)/len(anomalies)*100:.2f}%")
        
        return anomalies, threshold
    
    def analyze_latent_space(self, method='tsne'):
        """
        分析潜在空间
        
        Parameters:
        -----------
        method : str
            降维方法 ('tsne', 'pca')
            
        Returns:
        --------
        reduced_features : numpy.ndarray
            降维后的特征
        """
        if self.encoded_features is None:
            self.extract_latent_features()
        
        print(f"潜在空间分析 (方法: {method})...")
        
        if method == 'tsne':
            reducer = TSNE(n_components=2, random_state=42, perplexity=30)
        elif method == 'pca':
            reducer = PCA(n_components=2, random_state=42)
        else:
            raise ValueError(f"未知的降维方法: {method}")
        
        reduced_features = reducer.fit_transform(self.encoded_features)
        
        print(f"降维完成: {reduced_features.shape}")
        
        return reduced_features
    
    def cluster_analysis(self, n_clusters=3):
        """
        聚类分析
        
        Parameters:
        -----------
        n_clusters : int
            聚类数量
            
        Returns:
        --------
        cluster_labels : numpy.ndarray
            聚类标签
        silhouette : float
            轮廓系数
        """
        if self.encoded_features is None:
            self.extract_latent_features()
        
        print(f"聚类分析 (聚类数: {n_clusters})...")
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42)
        cluster_labels = kmeans.fit_predict(self.encoded_features)
        
        # 计算轮廓系数
        silhouette = silhouette_score(self.encoded_features, cluster_labels)
        
        print(f"聚类完成:")
        print(f"- 轮廓系数: {silhouette:.4f}")
        
        # 显示每个聚类的样本数
        unique, counts = np.unique(cluster_labels, return_counts=True)
        for cluster_id, count in zip(unique, counts):
            print(f"- 聚类 {cluster_id}: {count} 个样本")
        
        return cluster_labels, silhouette
    
    def comprehensive_visualization(self):
        """
        综合可视化分析
        """
        print("创建综合可视化分析...")
        
        # 确保所有数据都已计算
        if self.segments is None:
            self.load_and_preprocess_data()
        if self.encoded_features is None:
            self.extract_latent_features()
        if self.reconstruction_errors is None:
            self.calculate_reconstruction_errors()
        
        # 异常检测
        anomalies, threshold = self.detect_anomalies()
        
        # 降维分析
        tsne_features = self.analyze_latent_space('tsne')
        pca_features = self.analyze_latent_space('pca')
        
        # 聚类分析
        cluster_labels, silhouette = self.cluster_analysis(n_clusters=3)
        
        # 创建综合图表
        fig = plt.figure(figsize=(20, 15))
        
        # 1. 重建误差分布
        ax1 = plt.subplot(3, 3, 1)
        plt.hist(self.reconstruction_errors, bins=50, alpha=0.7, color='blue', edgecolor='black')
        plt.axvline(threshold, color='red', linestyle='--', label=f'异常阈值: {threshold:.4f}')
        plt.title('重建误差分布')
        plt.xlabel('重建误差')
        plt.ylabel('频次')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. 异常检测结果
        ax2 = plt.subplot(3, 3, 2)
        normal_errors = self.reconstruction_errors[~anomalies]
        anomaly_errors = self.reconstruction_errors[anomalies]
        plt.scatter(range(len(normal_errors)), normal_errors, c='blue', alpha=0.6, label='正常', s=20)
        if len(anomaly_errors) > 0:
            anomaly_indices = np.where(anomalies)[0]
            plt.scatter(anomaly_indices, anomaly_errors, c='red', alpha=0.8, label='异常', s=30)
        plt.title('异常检测结果')
        plt.xlabel('样本索引')
        plt.ylabel('重建误差')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 3. t-SNE可视化 (按重建误差着色)
        ax3 = plt.subplot(3, 3, 3)
        scatter = plt.scatter(tsne_features[:, 0], tsne_features[:, 1], 
                            c=self.reconstruction_errors, cmap='viridis', alpha=0.7)
        plt.colorbar(scatter, label='重建误差')
        plt.title('t-SNE可视化 (重建误差)')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        
        # 4. t-SNE可视化 (按异常着色)
        ax4 = plt.subplot(3, 3, 4)
        colors = ['blue' if not anomaly else 'red' for anomaly in anomalies]
        plt.scatter(tsne_features[:, 0], tsne_features[:, 1], c=colors, alpha=0.7)
        plt.title('t-SNE可视化 (异常检测)')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        
        # 5. PCA可视化
        ax5 = plt.subplot(3, 3, 5)
        scatter = plt.scatter(pca_features[:, 0], pca_features[:, 1], 
                            c=self.reconstruction_errors, cmap='plasma', alpha=0.7)
        plt.colorbar(scatter, label='重建误差')
        plt.title('PCA可视化')
        plt.xlabel('PC 1')
        plt.ylabel('PC 2')
        
        # 6. 聚类结果
        ax6 = plt.subplot(3, 3, 6)
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        for i in range(max(cluster_labels) + 1):
            mask = cluster_labels == i
            plt.scatter(tsne_features[mask, 0], tsne_features[mask, 1], 
                       c=colors[i % len(colors)], label=f'聚类 {i}', alpha=0.7)
        plt.title(f'聚类分析 (轮廓系数: {silhouette:.3f})')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        plt.legend()
        
        # 7. 样本重建对比
        ax7 = plt.subplot(3, 3, 7)
        # 选择一个异常样本和一个正常样本
        if np.any(anomalies):
            anomaly_idx = np.where(anomalies)[0][0]
            normal_idx = np.where(~anomalies)[0][0]
            
            reconstructed = self.autoencoder.predict(self.segments[[anomaly_idx, normal_idx]], verbose=0)
            
            time = np.arange(256) / 128
            plt.plot(time, self.segments[normal_idx], 'b-', label='正常原始', linewidth=1.5)
            plt.plot(time, reconstructed[1], 'b--', label='正常重建', linewidth=1.5)
            plt.plot(time, self.segments[anomaly_idx] + 2, 'r-', label='异常原始', linewidth=1.5)
            plt.plot(time, reconstructed[0] + 2, 'r--', label='异常重建', linewidth=1.5)
            plt.title('重建对比示例')
            plt.xlabel('时间 (秒)')
            plt.ylabel('幅值 (标准化)')
            plt.legend()
            plt.grid(True, alpha=0.3)
        
        # 8. 潜在特征分布
        ax8 = plt.subplot(3, 3, 8)
        feature_means = np.mean(self.encoded_features, axis=0)
        plt.bar(range(len(feature_means)), feature_means, alpha=0.7, color='green')
        plt.title('潜在特征平均值')
        plt.xlabel('特征维度')
        plt.ylabel('平均值')
        plt.grid(True, alpha=0.3)
        
        # 9. 误差与样本索引关系
        ax9 = plt.subplot(3, 3, 9)
        sample_indices = [info['sample_idx'] for info in self.segment_info]
        plt.scatter(sample_indices, self.reconstruction_errors, alpha=0.6, c=anomalies, cmap='coolwarm')
        plt.title('重建误差 vs 原始样本')
        plt.xlabel('原始样本索引')
        plt.ylabel('重建误差')
        plt.grid(True, alpha=0.3)
        
        plt.suptitle('癫痫自动编码器综合分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('comprehensive_autoencoder_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return {
            'anomalies': anomalies,
            'threshold': threshold,
            'cluster_labels': cluster_labels,
            'silhouette_score': silhouette,
            'tsne_features': tsne_features,
            'pca_features': pca_features
        }


def main():
    """
    主函数 - 执行高级自动编码器分析
    """
    print("=" * 60)
    print("高级癫痫自动编码器分析")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = AdvancedAutoencoderAnalysis()
    
    # 加载数据
    analyzer.load_and_preprocess_data()
    
    # 执行综合分析
    results = analyzer.comprehensive_visualization()
    
    print("\n分析结果总结:")
    print(f"- 检测到 {np.sum(results['anomalies'])} 个异常段")
    print(f"- 聚类轮廓系数: {results['silhouette_score']:.4f}")
    print(f"- 异常阈值: {results['threshold']:.6f}")
    
    print("\n高级分析完成! 查看生成的可视化文件。")


if __name__ == "__main__":
    main()
