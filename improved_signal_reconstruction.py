#!/usr/bin/env python3
"""
改进的EEG信号重建与对比可视化
Improved EEG Signal Reconstruction and Comparison Visualization

改进点:
1. 更好的信号重建算法
2. 基于小波逆变换的重建
3. 更准确的特征到信号映射
4. 修复相关性计算问题
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from sklearn.preprocessing import RobustScaler
import warnings
import os
import gzip
import json
import joblib
import pywt
from scipy import signal as scipy_signal
from scipy.stats import skew, kurtosis
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class ImprovedEEGReconstructor:
    """
    改进的EEG信号重建器
    """
    
    def __init__(self, segment_length=512, sampling_rate=128):
        self.segment_length = segment_length
        self.sampling_rate = sampling_rate
        
        # 模型和标准化器
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        print(f"改进的EEG信号重建器初始化:")
        print(f"- 信号长度: {segment_length} 样本点 (4秒)")
        print(f"- 采样率: {sampling_rate} Hz")
    
    def load_models_and_data(self):
        """
        加载模型和数据
        """
        print("加载模型和数据...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        return True
    
    def improved_signal_reconstruction(self, fake_features, reference_signals):
        """
        改进的信号重建方法
        基于参考信号的结构和伪装特征的统计信息
        """
        print("使用改进方法重建EEG信号...")
        
        reconstructed_signals = []
        
        for i, (fake_feature, ref_signal) in enumerate(zip(fake_features, reference_signals)):
            # 方法1: 基于小波重建
            reconstructed_wavelet = self.wavelet_based_reconstruction(fake_feature, ref_signal)
            
            # 方法2: 基于频域重建
            reconstructed_freq = self.frequency_based_reconstruction(fake_feature, ref_signal)
            
            # 方法3: 混合重建 (加权平均)
            alpha = 0.7  # 小波重建权重
            beta = 0.3   # 频域重建权重
            
            reconstructed_mixed = alpha * reconstructed_wavelet + beta * reconstructed_freq
            
            # 后处理: 调整统计特性以匹配伪装特征
            reconstructed_final = self.post_process_signal(reconstructed_mixed, fake_feature)
            
            reconstructed_signals.append(reconstructed_final)
        
        return np.array(reconstructed_signals)
    
    def wavelet_based_reconstruction(self, fake_feature, reference_signal):
        """
        基于小波的信号重建
        """
        # 对参考信号进行小波分解
        coeffs_ref = pywt.wavedec(reference_signal, 'db4', level=5)
        
        # 从伪装特征中提取小波相关信息
        # 假设特征的前67维是小波特征
        wavelet_features = fake_feature[:67]
        
        # 重建小波系数
        coeffs_new = []
        
        # 近似系数 (9个特征)
        cA_features = wavelet_features[:9]
        cA_std = max(abs(cA_features[1]), 0.01)  # 确保标准差为正
        cA_new = np.random.normal(cA_features[0], cA_std, len(coeffs_ref[0]))
        coeffs_new.append(cA_new)

        # 细节系数 (每层10个特征)
        feature_idx = 9
        for level in range(5):  # 5层细节系数
            cD_features = wavelet_features[feature_idx:feature_idx+10]
            cD_std = max(abs(cD_features[1]), 0.01)  # 确保标准差为正
            cD_new = np.random.normal(cD_features[0], cD_std, len(coeffs_ref[level+1]))
            coeffs_new.append(cD_new)
            feature_idx += 10
        
        # 小波逆变换
        reconstructed = pywt.waverec(coeffs_new, 'db4')
        
        # 调整长度
        if len(reconstructed) > self.segment_length:
            reconstructed = reconstructed[:self.segment_length]
        elif len(reconstructed) < self.segment_length:
            reconstructed = np.pad(reconstructed, (0, self.segment_length - len(reconstructed)), 'edge')
        
        return reconstructed
    
    def frequency_based_reconstruction(self, fake_feature, reference_signal):
        """
        基于频域的信号重建
        """
        # 从伪装特征中提取频域信息
        # 假设特征的82-93维是频域特征
        freq_features = fake_feature[82:94]
        
        # 构建频谱
        freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)
        
        # 频带功率 (相对功率)
        band_powers = freq_features[5:10]  # Delta, Theta, Alpha, Beta, Gamma
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        
        # 构建功率谱密度
        psd = np.zeros(len(freqs))
        
        for i, (low, high) in enumerate(bands):
            band_mask = (np.abs(freqs) >= low) & (np.abs(freqs) <= high)
            if np.sum(band_mask) > 0:
                psd[band_mask] = band_powers[i] / np.sum(band_mask)
        
        # 添加噪声以增加真实性
        noise_level = 0.1
        psd += np.random.exponential(noise_level, len(psd))
        
        # 使用参考信号的相位信息
        ref_fft = np.fft.fft(reference_signal)
        ref_phases = np.angle(ref_fft)
        
        # 构建复数频谱
        spectrum = np.sqrt(psd) * np.exp(1j * ref_phases)
        
        # 确保共轭对称性
        spectrum[len(freqs)//2+1:] = np.conj(spectrum[1:len(freqs)//2][::-1])
        
        # 逆FFT
        reconstructed = np.real(np.fft.ifft(spectrum))
        
        return reconstructed
    
    def post_process_signal(self, signal, fake_feature):
        """
        后处理信号以匹配伪装特征的统计特性
        """
        # 从伪装特征中提取时域统计信息
        # 假设特征的67-81维是时域特征
        time_features = fake_feature[67:82]
        
        target_mean = time_features[0]
        target_std = time_features[1]
        
        # 标准化信号
        signal_normalized = (signal - np.mean(signal)) / (np.std(signal) + 1e-8)
        
        # 调整到目标统计特性
        signal_adjusted = signal_normalized * target_std + target_mean
        
        # 平滑处理
        from scipy.ndimage import gaussian_filter1d
        signal_smoothed = gaussian_filter1d(signal_adjusted, sigma=1.0)
        
        return signal_smoothed
    
    def create_comprehensive_comparison(self, original_signals, original_features):
        """
        创建综合对比可视化
        """
        print("创建综合对比可视化...")
        
        # 1. 生成伪装特征
        fake_healthy_features = self.generator.predict(original_features, verbose=0)
        
        # 2. 使用改进方法重建信号
        reconstructed_signals = self.improved_signal_reconstruction(fake_healthy_features, original_signals)
        
        # 3. 分类器预测
        original_pred = self.classifier.predict(original_features, verbose=0)
        fake_pred = self.classifier.predict(fake_healthy_features, verbose=0)
        
        # 4. 计算信号相似性 (修复NaN问题)
        signal_correlations = []
        for i in range(len(original_signals)):
            orig = original_signals[i]
            recon = reconstructed_signals[i]
            
            # 确保信号有变异性
            if np.std(orig) > 1e-8 and np.std(recon) > 1e-8:
                correlation = np.corrcoef(orig, recon)[0, 1]
                if not np.isnan(correlation):
                    signal_correlations.append(correlation)
                else:
                    signal_correlations.append(0.0)
            else:
                signal_correlations.append(0.0)
        
        # 5. 创建主要对比图
        n_samples = len(original_signals)
        fig, axes = plt.subplots(n_samples, 4, figsize=(20, 4*n_samples))
        
        if n_samples == 1:
            axes = axes.reshape(1, -1)
        
        time_axis = np.arange(self.segment_length) / self.sampling_rate
        
        for i in range(n_samples):
            # 原始癫痫信号
            axes[i, 0].plot(time_axis, original_signals[i], 'r-', linewidth=1.5, alpha=0.8)
            axes[i, 0].set_title(f'样本 {i+1}: 原始癫痫信号\n'
                                f'分类器预测: {original_pred[i][0]:.3f}', 
                                fontsize=11, fontweight='bold')
            axes[i, 0].set_xlabel('时间 (秒)')
            axes[i, 0].set_ylabel('标准化幅值')
            axes[i, 0].grid(True, alpha=0.3)
            axes[i, 0].set_ylim(-4, 4)
            
            # 伪装重建信号
            axes[i, 1].plot(time_axis, reconstructed_signals[i], 'g-', linewidth=1.5, alpha=0.8)
            axes[i, 1].set_title(f'样本 {i+1}: 伪装重建信号\n'
                                f'分类器预测: {fake_pred[i][0]:.3f}', 
                                fontsize=11, fontweight='bold')
            axes[i, 1].set_xlabel('时间 (秒)')
            axes[i, 1].set_ylabel('标准化幅值')
            axes[i, 1].grid(True, alpha=0.3)
            axes[i, 1].set_ylim(-4, 4)
            
            # 信号叠加对比
            axes[i, 2].plot(time_axis, original_signals[i], 'r-', linewidth=1.5, 
                           alpha=0.7, label='原始癫痫信号')
            axes[i, 2].plot(time_axis, reconstructed_signals[i], 'g--', linewidth=1.5, 
                           alpha=0.7, label='伪装重建信号')
            axes[i, 2].set_title(f'样本 {i+1}: 信号叠加对比\n'
                                f'相关性: {signal_correlations[i]:.3f}', 
                                fontsize=11, fontweight='bold')
            axes[i, 2].set_xlabel('时间 (秒)')
            axes[i, 2].set_ylabel('标准化幅值')
            axes[i, 2].legend()
            axes[i, 2].grid(True, alpha=0.3)
            axes[i, 2].set_ylim(-4, 4)
            
            # 功率谱对比
            freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)[:self.segment_length//2]
            
            original_fft = np.fft.fft(original_signals[i])
            original_psd = np.abs(original_fft[:self.segment_length//2]) ** 2
            
            reconstructed_fft = np.fft.fft(reconstructed_signals[i])
            reconstructed_psd = np.abs(reconstructed_fft[:self.segment_length//2]) ** 2
            
            axes[i, 3].semilogy(freqs, original_psd, 'r-', linewidth=2, 
                               alpha=0.7, label='原始信号')
            axes[i, 3].semilogy(freqs, reconstructed_psd, 'g--', linewidth=2, 
                               alpha=0.7, label='重建信号')
            axes[i, 3].set_title(f'样本 {i+1}: 功率谱对比', fontsize=11, fontweight='bold')
            axes[i, 3].set_xlabel('频率 (Hz)')
            axes[i, 3].set_ylabel('功率谱密度')
            axes[i, 3].legend()
            axes[i, 3].grid(True, alpha=0.3)
            axes[i, 3].set_xlim(0, 60)
        
        plt.tight_layout()
        plt.savefig('改进的癫痫信号vs伪装重建信号对比.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("综合对比可视化完成!")
        
        return {
            'original_signals': original_signals,
            'reconstructed_signals': reconstructed_signals,
            'original_features': original_features,
            'fake_features': fake_healthy_features,
            'original_pred': original_pred,
            'fake_pred': fake_pred,
            'signal_correlations': signal_correlations
        }


def main():
    """
    主函数
    """
    print("=" * 80)
    print("改进的EEG信号重建与对比可视化")
    print("=" * 80)
    
    # 初始化重建器
    reconstructor = ImprovedEEGReconstructor()
    
    # 加载模型和数据
    if not reconstructor.load_models_and_data():
        print("❌ 模型和数据加载失败")
        return
    
    # 加载高置信度癫痫特征和对应的原始信号
    high_conf_epilepsy_features = np.load('confidence_gan_results/high_conf_epilepsy_features.npy')
    
    # 选择前5个样本进行演示
    sample_features = high_conf_epilepsy_features[:5]
    
    # 生成一些模拟的原始信号作为参考
    # 在实际应用中，这些应该是对应的真实EEG信号段
    np.random.seed(42)
    sample_signals = []
    for i in range(5):
        # 生成具有癫痫特征的模拟信号
        t = np.arange(512) / 128
        signal = (np.sin(2*np.pi*10*t) + 0.5*np.sin(2*np.pi*20*t) + 
                 0.3*np.random.randn(512) + 
                 2*np.exp(-((t-2)**2)/0.1) * np.sin(2*np.pi*40*t))  # 癫痫样放电
        signal = (signal - np.mean(signal)) / np.std(signal)
        sample_signals.append(signal)
    
    sample_signals = np.array(sample_signals)
    
    print(f"使用样本数据:")
    print(f"- 特征形状: {sample_features.shape}")
    print(f"- 信号形状: {sample_signals.shape}")
    
    # 创建综合对比
    results = reconstructor.create_comprehensive_comparison(sample_signals, sample_features)
    
    print("\n" + "=" * 80)
    print("改进的EEG信号重建完成!")
    print("=" * 80)
    print("主要发现:")
    print(f"- 原始癫痫信号平均预测概率: {np.mean(results['original_pred']):.3f}")
    print(f"- 伪装重建信号平均预测概率: {np.mean(results['fake_pred']):.3f}")
    print(f"- 平均伪装效果: {np.mean(1 - results['fake_pred']):.3f}")
    print(f"- 平均信号相关性: {np.mean(results['signal_correlations']):.3f}")
    print(f"- 特征空间平均距离: {np.mean(np.linalg.norm(results['original_features'] - results['fake_features'], axis=1)):.3f}")
    
    print("\n🎯 关键成果:")
    print(f"✅ 成功重建了 {len(results['reconstructed_signals'])} 个伪装EEG信号")
    print(f"✅ 平均伪装成功率: {np.mean(1 - results['fake_pred'])*100:.1f}%")
    print(f"✅ 信号结构保持度: {np.mean(results['signal_correlations']):.3f}")
    
    return reconstructor, results


if __name__ == "__main__":
    reconstructor, results = main()
