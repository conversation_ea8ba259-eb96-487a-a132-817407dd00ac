#!/usr/bin/env python3
"""
病灶定位系统 - 基于EEG信号重建的异常位置检测
Lesion Localization System - Abnormal Position Detection via EEG Signal Reconstruction

目标:
1. 从伪装特征重建完整的EEG信号
2. 对比原始癫痫信号 vs 伪装重建信号
3. 定位具体哪个时间点/位置有信号异常
4. 量化异常程度和持续时间
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
import warnings
import os
import gzip
import joblib
import pywt
from scipy import signal as scipy_signal
from scipy.fft import dct, idct
from scipy.optimize import minimize
from sklearn.metrics import mean_squared_error
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class LesionLocalizationSystem:
    """
    病灶定位系统
    """
    
    def __init__(self, segment_length=512, sampling_rate=128):
        self.segment_length = segment_length
        self.sampling_rate = sampling_rate
        
        # 模型和标准化器
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        print(f"病灶定位系统初始化:")
        print(f"- 信号长度: {segment_length} 样本点 ({segment_length/sampling_rate:.1f}秒)")
        print(f"- 采样率: {sampling_rate} Hz")
        print(f"- 时间分辨率: {1000/sampling_rate:.1f} ms")
    
    def load_models(self):
        """
        加载模型
        """
        print("加载模型...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        return True
    
    def advanced_signal_reconstruction(self, fake_features, original_signals):
        """
        高级信号重建方法 - 基于优化的逆向工程
        """
        print("使用高级优化方法重建EEG信号...")
        
        reconstructed_signals = []
        
        for i, (fake_feature, original_signal) in enumerate(zip(fake_features, original_signals)):
            print(f"重建信号 {i+1}/{len(fake_features)}...")
            
            # 使用优化方法重建信号
            reconstructed_signal = self.optimize_signal_reconstruction(fake_feature, original_signal)
            
            reconstructed_signals.append(reconstructed_signal)
        
        return np.array(reconstructed_signals)
    
    def optimize_signal_reconstruction(self, target_features, reference_signal):
        """
        基于优化的信号重建
        """
        # 初始化：使用参考信号作为起点
        initial_signal = reference_signal.copy()
        
        # 定义目标函数：最小化特征差异
        def objective_function(signal_params):
            # 重构信号
            reconstructed_signal = self.params_to_signal(signal_params, reference_signal)
            
            # 提取特征
            try:
                reconstructed_features = self.extract_features_from_signal(reconstructed_signal)
                
                # 计算特征差异
                feature_diff = np.sum((reconstructed_features - target_features) ** 2)
                
                # 添加信号平滑性约束
                smoothness_penalty = np.sum(np.diff(reconstructed_signal) ** 2) * 0.001
                
                return feature_diff + smoothness_penalty
            
            except:
                return 1e6  # 如果出错，返回大的惩罚值
        
        # 参数化信号（使用DCT系数）
        initial_params = self.signal_to_params(initial_signal)
        
        # 优化
        try:
            result = minimize(
                objective_function,
                initial_params,
                method='L-BFGS-B',
                options={'maxiter': 100, 'disp': False}
            )
            
            # 重构最优信号
            optimized_signal = self.params_to_signal(result.x, reference_signal)
            
            return optimized_signal
        
        except:
            # 如果优化失败，使用备用方法
            return self.fallback_reconstruction(target_features, reference_signal)
    
    def signal_to_params(self, signal):
        """
        将信号转换为参数（使用DCT）
        """
        # 使用离散余弦变换
        dct_coeffs = dct(signal, norm='ortho')

        # 只保留前64个系数（降维）
        return dct_coeffs[:64]

    def params_to_signal(self, params, reference_signal):
        """
        从参数重构信号
        """
        # 扩展参数到完整长度
        full_coeffs = np.zeros(len(reference_signal))
        full_coeffs[:len(params)] = params

        # 逆DCT
        reconstructed = idct(full_coeffs, norm='ortho')
        
        # 确保长度正确
        if len(reconstructed) != self.segment_length:
            reconstructed = np.interp(
                np.linspace(0, 1, self.segment_length),
                np.linspace(0, 1, len(reconstructed)),
                reconstructed
            )
        
        return reconstructed
    
    def fallback_reconstruction(self, target_features, reference_signal):
        """
        备用重建方法：基于小波和频域合成
        """
        # 方法1：小波重建
        wavelet_recon = self.wavelet_reconstruction(target_features, reference_signal)
        
        # 方法2：频域重建
        freq_recon = self.frequency_reconstruction(target_features, reference_signal)
        
        # 方法3：时域调整
        time_recon = self.time_domain_adjustment(target_features, reference_signal)
        
        # 加权组合
        combined = 0.5 * wavelet_recon + 0.3 * freq_recon + 0.2 * time_recon
        
        return combined
    
    def wavelet_reconstruction(self, target_features, reference_signal):
        """
        基于小波的重建
        """
        # 对参考信号进行小波分解
        coeffs_ref = pywt.wavedec(reference_signal, 'db4', level=5)
        
        # 从目标特征中提取小波统计信息
        wavelet_features = target_features[:67]  # 前67维是小波特征
        
        # 重建小波系数
        coeffs_new = []
        
        # 近似系数（9个特征）
        cA_stats = wavelet_features[:9]
        cA_new = np.random.normal(cA_stats[0], max(abs(cA_stats[1]), 0.01), len(coeffs_ref[0]))
        coeffs_new.append(cA_new)
        
        # 细节系数（每层10个特征）
        feature_idx = 9
        for level in range(5):
            cD_stats = wavelet_features[feature_idx:feature_idx+10]
            cD_new = np.random.normal(cD_stats[0], max(abs(cD_stats[1]), 0.01), len(coeffs_ref[level+1]))
            coeffs_new.append(cD_new)
            feature_idx += 10
        
        # 小波逆变换
        reconstructed = pywt.waverec(coeffs_new, 'db4')
        
        # 调整长度
        if len(reconstructed) > self.segment_length:
            reconstructed = reconstructed[:self.segment_length]
        elif len(reconstructed) < self.segment_length:
            reconstructed = np.pad(reconstructed, (0, self.segment_length - len(reconstructed)), 'edge')
        
        return reconstructed
    
    def frequency_reconstruction(self, target_features, reference_signal):
        """
        基于频域的重建
        """
        # 从目标特征中提取频域信息
        freq_features = target_features[82:94]  # 频域特征
        
        # 构建目标功率谱
        freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)
        
        # 频带功率
        band_powers = freq_features[5:10]  # 相对频带功率
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        
        # 构建功率谱密度
        psd = np.ones(len(freqs)) * 0.01  # 基础噪声
        
        for i, (low, high) in enumerate(bands):
            band_mask = (np.abs(freqs) >= low) & (np.abs(freqs) <= high)
            if np.sum(band_mask) > 0:
                psd[band_mask] += band_powers[i] / np.sum(band_mask)
        
        # 使用参考信号的相位
        ref_fft = np.fft.fft(reference_signal)
        ref_phases = np.angle(ref_fft)
        
        # 构建复数频谱
        spectrum = np.sqrt(psd) * np.exp(1j * ref_phases)
        
        # 确保共轭对称性
        spectrum[len(freqs)//2+1:] = np.conj(spectrum[1:len(freqs)//2][::-1])
        
        # 逆FFT
        reconstructed = np.real(np.fft.ifft(spectrum))
        
        return reconstructed
    
    def time_domain_adjustment(self, target_features, reference_signal):
        """
        基于时域统计的调整
        """
        # 从目标特征中提取时域统计信息
        time_features = target_features[67:82]  # 时域特征
        
        target_mean = time_features[0]
        target_std = time_features[1]
        target_skew = time_features[13] if len(time_features) > 13 else 0
        target_kurt = time_features[14] if len(time_features) > 14 else 0
        
        # 调整参考信号的统计特性
        adjusted = reference_signal.copy()
        
        # 标准化
        adjusted = (adjusted - np.mean(adjusted)) / (np.std(adjusted) + 1e-8)
        
        # 调整到目标统计特性
        adjusted = adjusted * target_std + target_mean
        
        # 简单的偏度和峰度调整（通过非线性变换）
        if abs(target_skew) > 0.1:
            adjusted = np.sign(adjusted) * np.power(np.abs(adjusted), 1 + target_skew * 0.1)
        
        return adjusted

    def extract_features_from_signal(self, signal):
        """
        从信号提取特征（与训练时保持一致）
        """
        # 小波特征
        wavelet_features = self.extract_wavelet_features(signal)

        # 时域特征
        time_features = self.extract_time_domain_features(signal)

        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal)

        # 拼接特征
        all_features = np.hstack([wavelet_features, time_features, freq_features])

        # 标准化
        features_scaled = self.feature_scaler.transform(all_features.reshape(1, -1)).flatten()

        return features_scaled

    def extract_wavelet_features(self, signal):
        """提取小波特征"""
        coeffs = pywt.wavedec(signal, 'db4', level=5)
        features = []

        # 近似系数
        cA = coeffs[0]
        features.extend([
            np.mean(cA), np.std(cA), np.var(cA),
            np.max(cA), np.min(cA),
            np.percentile(cA, 25), np.percentile(cA, 75),
            np.sum(cA ** 2),
            -np.sum(np.abs(cA) * np.log2(np.abs(cA) + 1e-12))
        ])

        # 细节系数
        for cD in coeffs[1:]:
            features.extend([
                np.mean(cD), np.std(cD), np.var(cD),
                np.max(cD), np.min(cD),
                np.percentile(cD, 25), np.percentile(cD, 75),
                np.sum(cD ** 2),
                -np.sum(np.abs(cD) * np.log2(np.abs(cD) + 1e-12)),
                np.sum(np.diff(np.sign(cD)) != 0) / len(cD)
            ])

        # 能量比
        energies = [np.sum(c ** 2) for c in coeffs]
        total_energy = sum(energies)
        energy_ratios = [e/total_energy for e in energies]
        features.extend(energy_ratios)

        # 相对小波能量
        features.extend([
            energies[0] / energies[1] if energies[1] > 0 else 0,
            max(energies[1:]) / energies[0] if energies[0] > 0 else 0
        ])

        return np.array(features)

    def extract_time_domain_features(self, signal):
        """提取时域特征"""
        from scipy import stats

        features = [
            np.mean(signal), np.std(signal), np.var(signal),
            np.max(signal), np.min(signal), np.ptp(signal),
            np.percentile(signal, 25), np.percentile(signal, 75),
            np.median(signal), np.mean(np.abs(signal)),
            np.sqrt(np.mean(signal**2)),
            len(signal[signal > 0]) / len(signal),
            np.sum(np.diff(signal) > 0) / len(signal),
            stats.kurtosis(signal), stats.skew(signal)
        ]

        return np.array(features)

    def extract_frequency_domain_features(self, signal):
        """提取频域特征"""
        # FFT
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/self.sampling_rate)
        psd = np.abs(fft) ** 2

        # 频带划分
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        band_powers = []

        for low, high in bands:
            band_mask = (freqs >= low) & (freqs <= high)
            band_power = np.sum(psd[band_mask])
            band_powers.append(band_power)

        total_power = np.sum(band_powers)

        features = [
            np.mean(psd), np.std(psd), np.max(psd),
            freqs[np.argmax(psd)], total_power
        ]

        # 相对频带功率
        if total_power > 0:
            features.extend([p/total_power for p in band_powers])
        else:
            features.extend([0] * len(band_powers))

        # 频带功率比
        if band_powers[1] > 0:
            features.append(band_powers[0] / band_powers[1])
        else:
            features.append(0)

        if band_powers[0] > 0:
            features.append(band_powers[2] / band_powers[0])
        else:
            features.append(0)

        return np.array(features)

    def localize_lesions(self, original_signals, original_features):
        """
        病灶定位主函数
        """
        print("开始病灶定位分析...")

        # 1. 生成伪装特征
        fake_healthy_features = self.generator.predict(original_features, verbose=0)

        # 2. 重建伪装信号
        reconstructed_signals = self.advanced_signal_reconstruction(fake_healthy_features, original_signals)

        # 3. 计算信号差异
        signal_differences = original_signals - reconstructed_signals

        # 4. 分析异常位置
        lesion_locations = []

        for i, (orig, recon, diff) in enumerate(zip(original_signals, reconstructed_signals, signal_differences)):
            print(f"分析样本 {i+1}...")

            # 计算局部异常强度
            window_size = 32  # 250ms窗口 (32/128Hz)
            anomaly_scores = []

            for j in range(0, len(diff) - window_size + 1, window_size//4):
                window = diff[j:j+window_size]
                # 异常分数 = 均方根 + 方差
                anomaly_score = np.sqrt(np.mean(window**2)) + np.var(window)
                anomaly_scores.append(anomaly_score)

            # 找到异常峰值
            anomaly_scores = np.array(anomaly_scores)
            threshold = np.mean(anomaly_scores) + 2 * np.std(anomaly_scores)

            anomaly_peaks = []
            for k, score in enumerate(anomaly_scores):
                if score > threshold:
                    time_start = k * window_size // 4 / self.sampling_rate
                    time_end = (k * window_size // 4 + window_size) / self.sampling_rate
                    anomaly_peaks.append({
                        'start_time': time_start,
                        'end_time': time_end,
                        'intensity': score,
                        'sample_start': k * window_size // 4,
                        'sample_end': k * window_size // 4 + window_size
                    })

            lesion_locations.append({
                'sample_id': i,
                'anomaly_scores': anomaly_scores,
                'anomaly_peaks': anomaly_peaks,
                'total_anomaly': np.sum(anomaly_scores),
                'max_anomaly': np.max(anomaly_scores)
            })

        # 5. 创建病灶定位可视化
        self.create_lesion_localization_visualization(
            original_signals, reconstructed_signals, signal_differences, lesion_locations
        )

        return {
            'original_signals': original_signals,
            'reconstructed_signals': reconstructed_signals,
            'signal_differences': signal_differences,
            'lesion_locations': lesion_locations
        }

    def create_lesion_localization_visualization(self, original_signals, reconstructed_signals,
                                               signal_differences, lesion_locations):
        """
        创建病灶定位可视化
        """
        print("创建病灶定位可视化...")

        n_samples = len(original_signals)
        fig, axes = plt.subplots(n_samples, 4, figsize=(20, 5*n_samples))

        if n_samples == 1:
            axes = axes.reshape(1, -1)

        time_axis = np.arange(self.segment_length) / self.sampling_rate

        for i in range(n_samples):
            lesion_info = lesion_locations[i]

            # 1. 原始癫痫信号
            axes[i, 0].plot(time_axis, original_signals[i], 'r-', linewidth=1.5, alpha=0.8)
            axes[i, 0].set_title(f'样本 {i+1}: 原始癫痫EEG信号', fontsize=12, fontweight='bold')
            axes[i, 0].set_xlabel('时间 (秒)')
            axes[i, 0].set_ylabel('幅值')
            axes[i, 0].grid(True, alpha=0.3)

            # 2. 伪装重建信号
            axes[i, 1].plot(time_axis, reconstructed_signals[i], 'g-', linewidth=1.5, alpha=0.8)
            axes[i, 1].set_title(f'样本 {i+1}: 伪装重建EEG信号', fontsize=12, fontweight='bold')
            axes[i, 1].set_xlabel('时间 (秒)')
            axes[i, 1].set_ylabel('幅值')
            axes[i, 1].grid(True, alpha=0.3)

            # 3. 信号差异（病灶定位）
            axes[i, 2].plot(time_axis, signal_differences[i], 'b-', linewidth=1.5, alpha=0.8)

            # 标记异常区域
            for peak in lesion_info['anomaly_peaks']:
                start_idx = peak['sample_start']
                end_idx = min(peak['sample_end'], len(time_axis))
                axes[i, 2].axvspan(time_axis[start_idx], time_axis[end_idx-1],
                                  alpha=0.3, color='red',
                                  label=f'异常区域 (强度:{peak["intensity"]:.2f})')

            axes[i, 2].set_title(f'样本 {i+1}: 信号差异 (病灶定位)\n'
                                f'检测到 {len(lesion_info["anomaly_peaks"])} 个异常区域',
                                fontsize=12, fontweight='bold')
            axes[i, 2].set_xlabel('时间 (秒)')
            axes[i, 2].set_ylabel('差异幅值')
            axes[i, 2].grid(True, alpha=0.3)
            if lesion_info['anomaly_peaks']:
                axes[i, 2].legend()

            # 4. 异常强度时间序列
            window_size = 32
            anomaly_time_axis = np.arange(len(lesion_info['anomaly_scores'])) * (window_size//4) / self.sampling_rate

            axes[i, 3].plot(anomaly_time_axis, lesion_info['anomaly_scores'], 'purple',
                           linewidth=2, alpha=0.8)

            # 标记阈值线
            threshold = np.mean(lesion_info['anomaly_scores']) + 2 * np.std(lesion_info['anomaly_scores'])
            axes[i, 3].axhline(y=threshold, color='red', linestyle='--',
                              linewidth=2, label=f'异常阈值: {threshold:.2f}')

            # 标记异常峰值
            for peak in lesion_info['anomaly_peaks']:
                peak_time = peak['start_time'] + (peak['end_time'] - peak['start_time']) / 2
                axes[i, 3].scatter(peak_time, peak['intensity'],
                                  color='red', s=100, marker='*', zorder=5)

            axes[i, 3].set_title(f'样本 {i+1}: 异常强度时间序列\n'
                                f'最大异常: {lesion_info["max_anomaly"]:.2f}',
                                fontsize=12, fontweight='bold')
            axes[i, 3].set_xlabel('时间 (秒)')
            axes[i, 3].set_ylabel('异常强度')
            axes[i, 3].legend()
            axes[i, 3].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('病灶定位分析结果.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 创建详细的异常分析报告
        self.create_anomaly_analysis_report(lesion_locations)

        print("病灶定位可视化完成!")

    def create_anomaly_analysis_report(self, lesion_locations):
        """
        创建异常分析报告
        """
        print("创建异常分析报告...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))

        # 1. 异常区域数量统计
        anomaly_counts = [len(loc['anomaly_peaks']) for loc in lesion_locations]

        axes[0, 0].bar(range(len(anomaly_counts)), anomaly_counts,
                      color='skyblue', alpha=0.7, edgecolor='black')
        axes[0, 0].set_title('每个样本的异常区域数量', fontsize=14, fontweight='bold')
        axes[0, 0].set_xlabel('样本编号')
        axes[0, 0].set_ylabel('异常区域数量')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 异常强度分布
        all_intensities = []
        for loc in lesion_locations:
            for peak in loc['anomaly_peaks']:
                all_intensities.append(peak['intensity'])

        if all_intensities:
            axes[0, 1].hist(all_intensities, bins=20, alpha=0.7,
                           color='orange', edgecolor='black')
            axes[0, 1].set_title('异常强度分布', fontsize=14, fontweight='bold')
            axes[0, 1].set_xlabel('异常强度')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].grid(True, alpha=0.3)

        # 3. 异常持续时间分析
        durations = []
        for loc in lesion_locations:
            for peak in loc['anomaly_peaks']:
                duration = peak['end_time'] - peak['start_time']
                durations.append(duration * 1000)  # 转换为毫秒

        if durations:
            axes[1, 0].hist(durations, bins=15, alpha=0.7,
                           color='green', edgecolor='black')
            axes[1, 0].set_title('异常持续时间分布', fontsize=14, fontweight='bold')
            axes[1, 0].set_xlabel('持续时间 (毫秒)')
            axes[1, 0].set_ylabel('频次')
            axes[1, 0].grid(True, alpha=0.3)

        # 4. 异常时间分布（在4秒内的位置）
        anomaly_times = []
        for loc in lesion_locations:
            for peak in loc['anomaly_peaks']:
                center_time = (peak['start_time'] + peak['end_time']) / 2
                anomaly_times.append(center_time)

        if anomaly_times:
            axes[1, 1].hist(anomaly_times, bins=20, alpha=0.7,
                           color='red', edgecolor='black')
            axes[1, 1].set_title('异常在时间轴上的分布', fontsize=14, fontweight='bold')
            axes[1, 1].set_xlabel('时间 (秒)')
            axes[1, 1].set_ylabel('异常事件数量')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('异常分析统计报告.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 打印文字报告
        print("\n" + "="*60)
        print("病灶定位分析报告")
        print("="*60)

        total_anomalies = sum(len(loc['anomaly_peaks']) for loc in lesion_locations)
        print(f"总样本数: {len(lesion_locations)}")
        print(f"检测到的异常区域总数: {total_anomalies}")
        print(f"平均每个样本的异常区域数: {total_anomalies/len(lesion_locations):.1f}")

        if all_intensities:
            print(f"异常强度范围: {min(all_intensities):.3f} - {max(all_intensities):.3f}")
            print(f"平均异常强度: {np.mean(all_intensities):.3f}")

        if durations:
            print(f"异常持续时间范围: {min(durations):.1f} - {max(durations):.1f} 毫秒")
            print(f"平均持续时间: {np.mean(durations):.1f} 毫秒")

        print("\n详细异常位置:")
        for i, loc in enumerate(lesion_locations):
            print(f"\n样本 {i+1}:")
            if loc['anomaly_peaks']:
                for j, peak in enumerate(loc['anomaly_peaks']):
                    print(f"  异常区域 {j+1}: {peak['start_time']:.3f}s - {peak['end_time']:.3f}s "
                          f"(强度: {peak['intensity']:.3f})")
            else:
                print("  未检测到明显异常")

        print("="*60)


def load_real_epilepsy_data(num_samples=3):
    """
    加载真实癫痫数据进行病灶定位测试
    """
    print(f"加载真实癫痫数据 ({num_samples}个样本)...")

    # 加载Nigeria数据集元数据
    nigeria_metadata_path = "1252141/metadata_nigeria.csv"
    metadata = pd.read_csv(nigeria_metadata_path)

    # 获取癫痫组文件
    epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()

    epilepsy_signals = []
    epilepsy_features = []

    # 初始化特征提取器
    signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
    feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')

    def preprocess_signal(signal, sampling_rate=128):
        """预处理信号"""
        # 去除异常值
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        signal_clean = np.clip(signal, mean_val - 3*std_val, mean_val + 3*std_val)

        # 去趋势
        x = np.arange(len(signal_clean))
        coeffs = np.polyfit(x, signal_clean, 1)
        trend = np.polyval(coeffs, x)
        signal_detrend = signal_clean - trend

        # 带通滤波 (0.5-60Hz)
        nyquist = sampling_rate / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist

        if low_freq < 1 and high_freq < 1:
            b, a = scipy_signal.butter(4, [low_freq, high_freq], btype='band')
            signal_filtered = scipy_signal.filtfilt(b, a, signal_detrend)
        else:
            signal_filtered = signal_detrend

        # 标准化
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / (np.std(signal_filtered) + 1e-8)

        return signal_normalized

    def extract_simple_features(signal):
        """简化的特征提取"""
        # 小波特征
        coeffs = pywt.wavedec(signal, 'db4', level=5)
        features = []

        # 近似系数
        cA = coeffs[0]
        features.extend([
            np.mean(cA), np.std(cA), np.var(cA),
            np.max(cA), np.min(cA),
            np.percentile(cA, 25), np.percentile(cA, 75),
            np.sum(cA ** 2),
            -np.sum(np.abs(cA) * np.log2(np.abs(cA) + 1e-12))
        ])

        # 细节系数
        for cD in coeffs[1:]:
            features.extend([
                np.mean(cD), np.std(cD), np.var(cD),
                np.max(cD), np.min(cD),
                np.percentile(cD, 25), np.percentile(cD, 75),
                np.sum(cD ** 2),
                -np.sum(np.abs(cD) * np.log2(np.abs(cD) + 1e-12)),
                np.sum(np.diff(np.sign(cD)) != 0) / len(cD)
            ])

        # 能量比
        energies = [np.sum(c ** 2) for c in coeffs]
        total_energy = sum(energies)
        energy_ratios = [e/total_energy for e in energies]
        features.extend(energy_ratios)

        # 相对小波能量
        features.extend([
            energies[0] / energies[1] if energies[1] > 0 else 0,
            max(energies[1:]) / energies[0] if energies[0] > 0 else 0
        ])

        # 时域特征
        from scipy import stats
        time_features = [
            np.mean(signal), np.std(signal), np.var(signal),
            np.max(signal), np.min(signal), np.ptp(signal),
            np.percentile(signal, 25), np.percentile(signal, 75),
            np.median(signal), np.mean(np.abs(signal)),
            np.sqrt(np.mean(signal**2)),
            len(signal[signal > 0]) / len(signal),
            np.sum(np.diff(signal) > 0) / len(signal),
            stats.kurtosis(signal), stats.skew(signal)
        ]
        features.extend(time_features)

        # 频域特征
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/128)
        psd = np.abs(fft) ** 2

        # 频带划分
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        band_powers = []

        for low, high in bands:
            band_mask = (freqs >= low) & (freqs <= high)
            band_power = np.sum(psd[band_mask])
            band_powers.append(band_power)

        total_power = np.sum(band_powers)

        freq_features = [
            np.mean(psd), np.std(psd), np.max(psd),
            freqs[np.argmax(psd)], total_power
        ]

        # 相对频带功率
        if total_power > 0:
            freq_features.extend([p/total_power for p in band_powers])
        else:
            freq_features.extend([0] * len(band_powers))

        # 频带功率比
        if band_powers[1] > 0:
            freq_features.append(band_powers[0] / band_powers[1])
        else:
            freq_features.append(0)

        if band_powers[0] > 0:
            freq_features.append(band_powers[2] / band_powers[0])
        else:
            freq_features.append(0)

        features.extend(freq_features)

        return np.array(features)

    # 加载癫痫信号
    for filename in epilepsy_files[:10]:
        file_path = os.path.join("1252141/EEGs_Nigeria", filename)

        if os.path.exists(file_path):
            try:
                with gzip.open(file_path, 'rt') as f:
                    df = pd.read_csv(f)

                if 'FC5' in df.columns:
                    channel_data = df['FC5'].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values

                    # 预处理
                    channel_data = preprocess_signal(channel_data)

                    # 创建512点的信号段
                    segment_length = 512
                    step_size = segment_length // 2

                    for i in range(0, len(channel_data) - segment_length + 1, step_size):
                        segment = channel_data[i:i + segment_length]

                        if np.std(segment) > 0.01:
                            # 标准化
                            segment_scaled = signal_scaler.transform(segment.reshape(-1, 1)).flatten()

                            # 提取特征
                            features = extract_simple_features(segment_scaled)
                            features_scaled = feature_scaler.transform(features.reshape(1, -1)).flatten()

                            epilepsy_signals.append(segment_scaled)
                            epilepsy_features.append(features_scaled)

                            if len(epilepsy_signals) >= num_samples:
                                break

                    if len(epilepsy_signals) >= num_samples:
                        break

            except Exception as e:
                print(f"跳过文件 {filename}: {e}")

    epilepsy_signals = np.array(epilepsy_signals[:num_samples])
    epilepsy_features = np.array(epilepsy_features[:num_samples])

    print(f"癫痫数据加载完成:")
    print(f"- 信号形状: {epilepsy_signals.shape}")
    print(f"- 特征形状: {epilepsy_features.shape}")

    return epilepsy_signals, epilepsy_features


def main():
    """
    主函数
    """
    print("=" * 80)
    print("病灶定位系统 - 基于EEG信号重建的异常位置检测")
    print("=" * 80)

    # 初始化系统
    system = LesionLocalizationSystem()

    # 加载模型
    if not system.load_models():
        print("❌ 模型加载失败")
        return

    # 加载真实癫痫数据
    epilepsy_signals, epilepsy_features = load_real_epilepsy_data(num_samples=3)

    # 执行病灶定位分析
    print("\n开始病灶定位分析...")
    results = system.localize_lesions(epilepsy_signals, epilepsy_features)

    print("\n" + "=" * 80)
    print("病灶定位系统分析完成!")
    print("=" * 80)
    print("主要成果:")
    print(f"✅ 成功重建了 {len(results['reconstructed_signals'])} 个伪装EEG信号")
    print(f"✅ 检测到的异常区域总数: {sum(len(loc['anomaly_peaks']) for loc in results['lesion_locations'])}")
    print(f"✅ 平均每个样本的异常区域: {sum(len(loc['anomaly_peaks']) for loc in results['lesion_locations'])/len(results['lesion_locations']):.1f}")

    # 计算重建质量
    reconstruction_errors = []
    for orig, recon in zip(results['original_signals'], results['reconstructed_signals']):
        mse = np.mean((orig - recon) ** 2)
        reconstruction_errors.append(mse)

    print(f"✅ 平均重建误差: {np.mean(reconstruction_errors):.4f}")

    print("\n生成的可视化文件:")
    print("- 病灶定位分析结果.png")
    print("- 异常分析统计报告.png")

    print("\n🎯 病灶定位原理:")
    print("1. 原始癫痫信号包含病灶特征")
    print("2. GAN生成器创建伪装特征（掩盖病灶）")
    print("3. 从伪装特征重建信号（无病灶版本）")
    print("4. 原始信号 - 重建信号 = 病灶位置")
    print("5. 异常强度高的区域 = 病灶所在位置")

    return system, results


if __name__ == "__main__":
    system, results = main()
