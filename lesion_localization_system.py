#!/usr/bin/env python3
"""
病灶定位系统 - 基于EEG信号重建的异常位置检测
Lesion Localization System - Abnormal Position Detection via EEG Signal Reconstruction

目标:
1. 从伪装特征重建完整的EEG信号
2. 对比原始癫痫信号 vs 伪装重建信号
3. 定位具体哪个时间点/位置有信号异常
4. 量化异常程度和持续时间
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
import warnings
import os
import gzip
import joblib
import pywt
from scipy import signal as scipy_signal
from scipy.optimize import minimize
from sklearn.metrics import mean_squared_error
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class LesionLocalizationSystem:
    """
    病灶定位系统
    """
    
    def __init__(self, segment_length=512, sampling_rate=128):
        self.segment_length = segment_length
        self.sampling_rate = sampling_rate
        
        # 模型和标准化器
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        print(f"病灶定位系统初始化:")
        print(f"- 信号长度: {segment_length} 样本点 ({segment_length/sampling_rate:.1f}秒)")
        print(f"- 采样率: {sampling_rate} Hz")
        print(f"- 时间分辨率: {1000/sampling_rate:.1f} ms")
    
    def load_models(self):
        """
        加载模型
        """
        print("加载模型...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        return True
    
    def advanced_signal_reconstruction(self, fake_features, original_signals):
        """
        高级信号重建方法 - 基于优化的逆向工程
        """
        print("使用高级优化方法重建EEG信号...")
        
        reconstructed_signals = []
        
        for i, (fake_feature, original_signal) in enumerate(zip(fake_features, original_signals)):
            print(f"重建信号 {i+1}/{len(fake_features)}...")
            
            # 使用优化方法重建信号
            reconstructed_signal = self.optimize_signal_reconstruction(fake_feature, original_signal)
            
            reconstructed_signals.append(reconstructed_signal)
        
        return np.array(reconstructed_signals)
    
    def optimize_signal_reconstruction(self, target_features, reference_signal):
        """
        基于优化的信号重建
        """
        # 初始化：使用参考信号作为起点
        initial_signal = reference_signal.copy()
        
        # 定义目标函数：最小化特征差异
        def objective_function(signal_params):
            # 重构信号
            reconstructed_signal = self.params_to_signal(signal_params, reference_signal)
            
            # 提取特征
            try:
                reconstructed_features = self.extract_features_from_signal(reconstructed_signal)
                
                # 计算特征差异
                feature_diff = np.sum((reconstructed_features - target_features) ** 2)
                
                # 添加信号平滑性约束
                smoothness_penalty = np.sum(np.diff(reconstructed_signal) ** 2) * 0.001
                
                return feature_diff + smoothness_penalty
            
            except:
                return 1e6  # 如果出错，返回大的惩罚值
        
        # 参数化信号（使用DCT系数）
        initial_params = self.signal_to_params(initial_signal)
        
        # 优化
        try:
            result = minimize(
                objective_function,
                initial_params,
                method='L-BFGS-B',
                options={'maxiter': 100, 'disp': False}
            )
            
            # 重构最优信号
            optimized_signal = self.params_to_signal(result.x, reference_signal)
            
            return optimized_signal
        
        except:
            # 如果优化失败，使用备用方法
            return self.fallback_reconstruction(target_features, reference_signal)
    
    def signal_to_params(self, signal):
        """
        将信号转换为参数（使用DCT）
        """
        # 使用离散余弦变换
        dct_coeffs = scipy_signal.dct(signal, norm='ortho')
        
        # 只保留前64个系数（降维）
        return dct_coeffs[:64]
    
    def params_to_signal(self, params, reference_signal):
        """
        从参数重构信号
        """
        # 扩展参数到完整长度
        full_coeffs = np.zeros(len(reference_signal))
        full_coeffs[:len(params)] = params
        
        # 逆DCT
        reconstructed = scipy_signal.idct(full_coeffs, norm='ortho')
        
        # 确保长度正确
        if len(reconstructed) != self.segment_length:
            reconstructed = np.interp(
                np.linspace(0, 1, self.segment_length),
                np.linspace(0, 1, len(reconstructed)),
                reconstructed
            )
        
        return reconstructed
    
    def fallback_reconstruction(self, target_features, reference_signal):
        """
        备用重建方法：基于小波和频域合成
        """
        # 方法1：小波重建
        wavelet_recon = self.wavelet_reconstruction(target_features, reference_signal)
        
        # 方法2：频域重建
        freq_recon = self.frequency_reconstruction(target_features, reference_signal)
        
        # 方法3：时域调整
        time_recon = self.time_domain_adjustment(target_features, reference_signal)
        
        # 加权组合
        combined = 0.5 * wavelet_recon + 0.3 * freq_recon + 0.2 * time_recon
        
        return combined
    
    def wavelet_reconstruction(self, target_features, reference_signal):
        """
        基于小波的重建
        """
        # 对参考信号进行小波分解
        coeffs_ref = pywt.wavedec(reference_signal, 'db4', level=5)
        
        # 从目标特征中提取小波统计信息
        wavelet_features = target_features[:67]  # 前67维是小波特征
        
        # 重建小波系数
        coeffs_new = []
        
        # 近似系数（9个特征）
        cA_stats = wavelet_features[:9]
        cA_new = np.random.normal(cA_stats[0], max(abs(cA_stats[1]), 0.01), len(coeffs_ref[0]))
        coeffs_new.append(cA_new)
        
        # 细节系数（每层10个特征）
        feature_idx = 9
        for level in range(5):
            cD_stats = wavelet_features[feature_idx:feature_idx+10]
            cD_new = np.random.normal(cD_stats[0], max(abs(cD_stats[1]), 0.01), len(coeffs_ref[level+1]))
            coeffs_new.append(cD_new)
            feature_idx += 10
        
        # 小波逆变换
        reconstructed = pywt.waverec(coeffs_new, 'db4')
        
        # 调整长度
        if len(reconstructed) > self.segment_length:
            reconstructed = reconstructed[:self.segment_length]
        elif len(reconstructed) < self.segment_length:
            reconstructed = np.pad(reconstructed, (0, self.segment_length - len(reconstructed)), 'edge')
        
        return reconstructed
    
    def frequency_reconstruction(self, target_features, reference_signal):
        """
        基于频域的重建
        """
        # 从目标特征中提取频域信息
        freq_features = target_features[82:94]  # 频域特征
        
        # 构建目标功率谱
        freqs = np.fft.fftfreq(self.segment_length, 1/self.sampling_rate)
        
        # 频带功率
        band_powers = freq_features[5:10]  # 相对频带功率
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        
        # 构建功率谱密度
        psd = np.ones(len(freqs)) * 0.01  # 基础噪声
        
        for i, (low, high) in enumerate(bands):
            band_mask = (np.abs(freqs) >= low) & (np.abs(freqs) <= high)
            if np.sum(band_mask) > 0:
                psd[band_mask] += band_powers[i] / np.sum(band_mask)
        
        # 使用参考信号的相位
        ref_fft = np.fft.fft(reference_signal)
        ref_phases = np.angle(ref_fft)
        
        # 构建复数频谱
        spectrum = np.sqrt(psd) * np.exp(1j * ref_phases)
        
        # 确保共轭对称性
        spectrum[len(freqs)//2+1:] = np.conj(spectrum[1:len(freqs)//2][::-1])
        
        # 逆FFT
        reconstructed = np.real(np.fft.ifft(spectrum))
        
        return reconstructed
    
    def time_domain_adjustment(self, target_features, reference_signal):
        """
        基于时域统计的调整
        """
        # 从目标特征中提取时域统计信息
        time_features = target_features[67:82]  # 时域特征
        
        target_mean = time_features[0]
        target_std = time_features[1]
        target_skew = time_features[13] if len(time_features) > 13 else 0
        target_kurt = time_features[14] if len(time_features) > 14 else 0
        
        # 调整参考信号的统计特性
        adjusted = reference_signal.copy()
        
        # 标准化
        adjusted = (adjusted - np.mean(adjusted)) / (np.std(adjusted) + 1e-8)
        
        # 调整到目标统计特性
        adjusted = adjusted * target_std + target_mean
        
        # 简单的偏度和峰度调整（通过非线性变换）
        if abs(target_skew) > 0.1:
            adjusted = np.sign(adjusted) * np.power(np.abs(adjusted), 1 + target_skew * 0.1)
        
        return adjusted

    def extract_features_from_signal(self, signal):
        """
        从信号提取特征（与训练时保持一致）
        """
        # 小波特征
        wavelet_features = self.extract_wavelet_features(signal)

        # 时域特征
        time_features = self.extract_time_domain_features(signal)

        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal)

        # 拼接特征
        all_features = np.hstack([wavelet_features, time_features, freq_features])

        # 标准化
        features_scaled = self.feature_scaler.transform(all_features.reshape(1, -1)).flatten()

        return features_scaled

    def extract_wavelet_features(self, signal):
        """提取小波特征"""
        coeffs = pywt.wavedec(signal, 'db4', level=5)
        features = []

        # 近似系数
        cA = coeffs[0]
        features.extend([
            np.mean(cA), np.std(cA), np.var(cA),
            np.max(cA), np.min(cA),
            np.percentile(cA, 25), np.percentile(cA, 75),
            np.sum(cA ** 2),
            -np.sum(np.abs(cA) * np.log2(np.abs(cA) + 1e-12))
        ])

        # 细节系数
        for cD in coeffs[1:]:
            features.extend([
                np.mean(cD), np.std(cD), np.var(cD),
                np.max(cD), np.min(cD),
                np.percentile(cD, 25), np.percentile(cD, 75),
                np.sum(cD ** 2),
                -np.sum(np.abs(cD) * np.log2(np.abs(cD) + 1e-12)),
                np.sum(np.diff(np.sign(cD)) != 0) / len(cD)
            ])

        # 能量比
        energies = [np.sum(c ** 2) for c in coeffs]
        total_energy = sum(energies)
        energy_ratios = [e/total_energy for e in energies]
        features.extend(energy_ratios)

        # 相对小波能量
        features.extend([
            energies[0] / energies[1] if energies[1] > 0 else 0,
            max(energies[1:]) / energies[0] if energies[0] > 0 else 0
        ])

        return np.array(features)

    def extract_time_domain_features(self, signal):
        """提取时域特征"""
        from scipy import stats

        features = [
            np.mean(signal), np.std(signal), np.var(signal),
            np.max(signal), np.min(signal), np.ptp(signal),
            np.percentile(signal, 25), np.percentile(signal, 75),
            np.median(signal), np.mean(np.abs(signal)),
            np.sqrt(np.mean(signal**2)),
            len(signal[signal > 0]) / len(signal),
            np.sum(np.diff(signal) > 0) / len(signal),
            stats.kurtosis(signal), stats.skew(signal)
        ]

        return np.array(features)

    def extract_frequency_domain_features(self, signal):
        """提取频域特征"""
        # FFT
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/self.sampling_rate)
        psd = np.abs(fft) ** 2

        # 频带划分
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        band_powers = []

        for low, high in bands:
            band_mask = (freqs >= low) & (freqs <= high)
            band_power = np.sum(psd[band_mask])
            band_powers.append(band_power)

        total_power = np.sum(band_powers)

        features = [
            np.mean(psd), np.std(psd), np.max(psd),
            freqs[np.argmax(psd)], total_power
        ]

        # 相对频带功率
        if total_power > 0:
            features.extend([p/total_power for p in band_powers])
        else:
            features.extend([0] * len(band_powers))

        # 频带功率比
        if band_powers[1] > 0:
            features.append(band_powers[0] / band_powers[1])
        else:
            features.append(0)

        if band_powers[0] > 0:
            features.append(band_powers[2] / band_powers[0])
        else:
            features.append(0)

        return np.array(features)

    def localize_lesions(self, original_signals, original_features):
        """
        病灶定位主函数
        """
        print("开始病灶定位分析...")

        # 1. 生成伪装特征
        fake_healthy_features = self.generator.predict(original_features, verbose=0)

        # 2. 重建伪装信号
        reconstructed_signals = self.advanced_signal_reconstruction(fake_healthy_features, original_signals)

        # 3. 计算信号差异
        signal_differences = original_signals - reconstructed_signals

        # 4. 分析异常位置
        lesion_locations = []

        for i, (orig, recon, diff) in enumerate(zip(original_signals, reconstructed_signals, signal_differences)):
            print(f"分析样本 {i+1}...")

            # 计算局部异常强度
            window_size = 32  # 250ms窗口 (32/128Hz)
            anomaly_scores = []

            for j in range(0, len(diff) - window_size + 1, window_size//4):
                window = diff[j:j+window_size]
                # 异常分数 = 均方根 + 方差
                anomaly_score = np.sqrt(np.mean(window**2)) + np.var(window)
                anomaly_scores.append(anomaly_score)

            # 找到异常峰值
            anomaly_scores = np.array(anomaly_scores)
            threshold = np.mean(anomaly_scores) + 2 * np.std(anomaly_scores)

            anomaly_peaks = []
            for k, score in enumerate(anomaly_scores):
                if score > threshold:
                    time_start = k * window_size // 4 / self.sampling_rate
                    time_end = (k * window_size // 4 + window_size) / self.sampling_rate
                    anomaly_peaks.append({
                        'start_time': time_start,
                        'end_time': time_end,
                        'intensity': score,
                        'sample_start': k * window_size // 4,
                        'sample_end': k * window_size // 4 + window_size
                    })

            lesion_locations.append({
                'sample_id': i,
                'anomaly_scores': anomaly_scores,
                'anomaly_peaks': anomaly_peaks,
                'total_anomaly': np.sum(anomaly_scores),
                'max_anomaly': np.max(anomaly_scores)
            })

        # 5. 创建病灶定位可视化
        self.create_lesion_localization_visualization(
            original_signals, reconstructed_signals, signal_differences, lesion_locations
        )

        return {
            'original_signals': original_signals,
            'reconstructed_signals': reconstructed_signals,
            'signal_differences': signal_differences,
            'lesion_locations': lesion_locations
        }


def main():
    """
    主函数
    """
    print("=" * 80)
    print("病灶定位系统 - 基于EEG信号重建的异常位置检测")
    print("=" * 80)
    
    # 初始化系统
    system = LesionLocalizationSystem()
    
    # 加载模型
    if not system.load_models():
        print("❌ 模型加载失败")
        return
    
    return system


if __name__ == "__main__":
    system = main()
