#!/usr/bin/env python3
"""
Visualize Epilepsy Wavelet Dataset
Create visualizations and summary of the extracted epilepsy dataset
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings

warnings.filterwarnings('ignore')

def load_dataset(dataset_dir):
    """
    Load the epilepsy wavelet dataset
    
    Parameters:
    -----------
    dataset_dir : str
        Directory containing the dataset files
        
    Returns:
    --------
    gamma_waves : numpy.ndarray
        Gamma wave signals
    features : pandas.DataFrame
        Feature matrix
    metadata : pandas.DataFrame
        Metadata
    """
    dataset_path = Path(dataset_dir)
    
    # Load gamma waves
    gamma_waves = np.load(dataset_path / 'epilepsy_gamma_waves.npy')
    
    # Load features
    features = pd.read_csv(dataset_path / 'epilepsy_wavelet_features.csv')
    
    # Load metadata
    metadata = pd.read_csv(dataset_path / 'epilepsy_metadata.csv')
    
    return gamma_waves, features, metadata

def visualize_gamma_waves(gamma_waves, metadata, n_samples=5):
    """
    Visualize sample gamma waves
    
    Parameters:
    -----------
    gamma_waves : numpy.ndarray
        Gamma wave signals
    metadata : pandas.DataFrame
        Metadata
    n_samples : int
        Number of samples to visualize
    """
    fig, axes = plt.subplots(n_samples, 1, figsize=(15, 12))
    if n_samples == 1:
        axes = [axes]
    
    # Time axis (assuming 128 Hz sampling rate)
    time = np.arange(gamma_waves.shape[1]) / 128.0
    
    for i in range(min(n_samples, gamma_waves.shape[0])):
        axes[i].plot(time, gamma_waves[i], 'b-', linewidth=0.8)
        axes[i].set_title(f'Gamma Wave - {metadata.iloc[i]["file_name"]} (Channel: {metadata.iloc[i]["key_channel"]})')
        axes[i].set_xlabel('Time (seconds)')
        axes[i].set_ylabel('Amplitude (μV)')
        axes[i].grid(True, alpha=0.3)
        axes[i].set_xlim(0, time[-1])
    
    plt.tight_layout()
    plt.savefig('epilepsy_gamma_waves_samples.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_channel_distribution(metadata):
    """
    Analyze the distribution of key channels
    
    Parameters:
    -----------
    metadata : pandas.DataFrame
        Metadata
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Channel distribution
    channel_counts = metadata['key_channel'].value_counts()
    ax1.bar(channel_counts.index, channel_counts.values, color='skyblue', edgecolor='navy')
    ax1.set_title('Distribution of Key Epilepsy Channels')
    ax1.set_xlabel('EEG Channel')
    ax1.set_ylabel('Number of Samples')
    ax1.tick_params(axis='x', rotation=45)
    
    # Channel index distribution
    idx_counts = metadata['key_channel_idx'].value_counts().sort_index()
    ax2.bar(idx_counts.index, idx_counts.values, color='lightcoral', edgecolor='darkred')
    ax2.set_title('Distribution of Channel Indices')
    ax2.set_xlabel('Channel Index')
    ax2.set_ylabel('Number of Samples')
    
    plt.tight_layout()
    plt.savefig('epilepsy_channel_distribution.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Channel Distribution:")
    print(channel_counts)

def analyze_features(features):
    """
    Analyze the extracted features
    
    Parameters:
    -----------
    features : pandas.DataFrame
        Feature matrix
    """
    # Feature correlation heatmap
    plt.figure(figsize=(20, 16))
    
    # Select key features for visualization
    gamma_features = [col for col in features.columns if col.startswith('Gamma_')]
    beta_features = [col for col in features.columns if col.startswith('Beta_')]
    
    # Create subplots for different frequency bands
    fig, axes = plt.subplots(2, 2, figsize=(20, 16))
    
    # Gamma features correlation
    gamma_corr = features[gamma_features].corr()
    sns.heatmap(gamma_corr, annot=True, cmap='coolwarm', center=0, ax=axes[0,0])
    axes[0,0].set_title('Gamma Band Features Correlation')
    
    # Beta features correlation
    beta_corr = features[beta_features].corr()
    sns.heatmap(beta_corr, annot=True, cmap='coolwarm', center=0, ax=axes[0,1])
    axes[0,1].set_title('Beta Band Features Correlation')
    
    # Feature distributions
    gamma_power_features = ['Gamma_rms', 'Gamma_variance', 'Gamma_spectral_centroid']
    for i, feature in enumerate(gamma_power_features):
        if i < 2:
            axes[1,i].hist(features[feature], bins=15, alpha=0.7, color='purple', edgecolor='black')
            axes[1,i].set_title(f'Distribution of {feature}')
            axes[1,i].set_xlabel(feature)
            axes[1,i].set_ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig('epilepsy_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_summary_statistics(gamma_waves, features, metadata):
    """
    Create summary statistics of the dataset
    
    Parameters:
    -----------
    gamma_waves : numpy.ndarray
        Gamma wave signals
    features : pandas.DataFrame
        Feature matrix
    metadata : pandas.DataFrame
        Metadata
    """
    print("=" * 60)
    print("EPILEPSY WAVELET DATASET SUMMARY")
    print("=" * 60)
    
    print(f"\nDataset Size:")
    print(f"- Number of epilepsy samples: {gamma_waves.shape[0]}")
    print(f"- Gamma wave length: {gamma_waves.shape[1]} samples")
    print(f"- Duration per sample: {gamma_waves.shape[1]/128:.2f} seconds")
    print(f"- Number of features: {features.shape[1]}")
    
    print(f"\nKey Channels Identified:")
    channel_summary = metadata['key_channel'].value_counts()
    for channel, count in channel_summary.items():
        percentage = (count / len(metadata)) * 100
        print(f"- {channel}: {count} samples ({percentage:.1f}%)")
    
    print(f"\nGamma Wave Statistics:")
    print(f"- Mean amplitude: {np.mean(gamma_waves):.4f} μV")
    print(f"- Standard deviation: {np.std(gamma_waves):.4f} μV")
    print(f"- Min amplitude: {np.min(gamma_waves):.4f} μV")
    print(f"- Max amplitude: {np.max(gamma_waves):.4f} μV")
    
    print(f"\nFeature Statistics (Gamma Band):")
    gamma_features = [col for col in features.columns if col.startswith('Gamma_')]
    for feature in gamma_features[:5]:  # Show first 5 gamma features
        mean_val = features[feature].mean()
        std_val = features[feature].std()
        print(f"- {feature}: {mean_val:.4f} ± {std_val:.4f}")
    
    print(f"\nMost Important Channels for Epilepsy Detection:")
    # Channels that appear most frequently
    top_channels = channel_summary.head(3)
    for i, (channel, count) in enumerate(top_channels.items(), 1):
        print(f"{i}. {channel} - appears in {count} samples")
    
    print(f"\nDataset Files Created:")
    print(f"- epilepsy_gamma_waves.npy: {gamma_waves.shape} array of gamma waves")
    print(f"- epilepsy_wavelet_features.csv: {features.shape} feature matrix")
    print(f"- epilepsy_metadata.csv: {metadata.shape} metadata table")

def create_comprehensive_visualization(gamma_waves, features, metadata):
    """
    Create a comprehensive visualization of the dataset
    
    Parameters:
    -----------
    gamma_waves : numpy.ndarray
        Gamma wave signals
    features : pandas.DataFrame
        Feature matrix
    metadata : pandas.DataFrame
        Metadata
    """
    fig = plt.figure(figsize=(20, 15))
    
    # Create a grid layout
    gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
    
    # 1. Sample gamma waves
    ax1 = fig.add_subplot(gs[0, :])
    time = np.arange(gamma_waves.shape[1]) / 128.0
    for i in range(min(3, gamma_waves.shape[0])):
        ax1.plot(time, gamma_waves[i] + i*50, label=f'{metadata.iloc[i]["key_channel"]}')
    ax1.set_title('Sample Gamma Waves from Key Epilepsy Channels', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Amplitude (μV) + offset')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Channel distribution
    ax2 = fig.add_subplot(gs[1, 0])
    channel_counts = metadata['key_channel'].value_counts()
    ax2.pie(channel_counts.values, labels=channel_counts.index, autopct='%1.1f%%')
    ax2.set_title('Key Channel Distribution')
    
    # 3. Gamma power distribution
    ax3 = fig.add_subplot(gs[1, 1])
    ax3.hist(features['Gamma_rms'], bins=15, alpha=0.7, color='red', edgecolor='black')
    ax3.set_title('Gamma Power Distribution')
    ax3.set_xlabel('RMS Power')
    ax3.set_ylabel('Frequency')
    
    # 4. Feature correlation (top features)
    ax4 = fig.add_subplot(gs[1, 2])
    key_features = ['Gamma_rms', 'Beta_rms', 'Alpha_rms', 'Theta_rms']
    corr_matrix = features[key_features].corr()
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=ax4)
    ax4.set_title('Band Power Correlations')
    
    # 5. Spectral features
    ax5 = fig.add_subplot(gs[2, 0])
    ax5.scatter(features['Gamma_spectral_centroid'], features['Gamma_spectral_entropy'], 
                c=features['key_channel_idx'], cmap='viridis', alpha=0.7)
    ax5.set_xlabel('Spectral Centroid')
    ax5.set_ylabel('Spectral Entropy')
    ax5.set_title('Gamma Spectral Features')
    
    # 6. Statistical features
    ax6 = fig.add_subplot(gs[2, 1])
    ax6.scatter(features['Gamma_skewness'], features['Gamma_kurtosis'], 
                c=features['key_channel_idx'], cmap='viridis', alpha=0.7)
    ax6.set_xlabel('Skewness')
    ax6.set_ylabel('Kurtosis')
    ax6.set_title('Gamma Statistical Features')
    
    # 7. Multi-band comparison
    ax7 = fig.add_subplot(gs[2, 2])
    band_powers = [features['Gamma_rms'].mean(), features['Beta_rms'].mean(), 
                   features['Alpha_rms'].mean(), features['Theta_rms'].mean(), 
                   features['Delta_rms'].mean()]
    bands = ['Gamma', 'Beta', 'Alpha', 'Theta', 'Delta']
    colors = ['red', 'orange', 'green', 'blue', 'purple']
    ax7.bar(bands, band_powers, color=colors, alpha=0.7, edgecolor='black')
    ax7.set_title('Average Power by Frequency Band')
    ax7.set_ylabel('RMS Power')
    ax7.tick_params(axis='x', rotation=45)
    
    plt.suptitle('Epilepsy Wavelet Dataset - Comprehensive Analysis', fontsize=16, fontweight='bold')
    plt.savefig('epilepsy_dataset_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    Main function to visualize the epilepsy dataset
    """
    dataset_dir = 'epilepsy_wavelet_dataset'
    
    # Load dataset
    print("Loading epilepsy wavelet dataset...")
    gamma_waves, features, metadata = load_dataset(dataset_dir)
    
    # Create summary statistics
    create_summary_statistics(gamma_waves, features, metadata)
    
    # Create visualizations
    print("\nCreating visualizations...")
    
    # Individual visualizations
    visualize_gamma_waves(gamma_waves, metadata, n_samples=5)
    analyze_channel_distribution(metadata)
    analyze_features(features)
    
    # Comprehensive visualization
    create_comprehensive_visualization(gamma_waves, features, metadata)
    
    print("\nVisualization complete! Check the generated PNG files.")

if __name__ == "__main__":
    main()
