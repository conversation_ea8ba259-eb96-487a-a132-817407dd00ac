#!/usr/bin/env python3
"""
渐进式改进 V1: 添加平滑性损失函数
Progressive Improvement V1: Add Smoothness Loss Function

改进点:
- 保持原有架构不变
- 只添加平滑性损失函数
- 测试效果后决定是否保留
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import warnings
import os
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

class SmoothLoss(keras.losses.Loss):
    """
    自定义平滑性损失函数
    结合重建损失和平滑性约束
    """
    def __init__(self, reconstruction_weight=1.0, smoothness_weight=0.1, name="smooth_loss"):
        super().__init__(name=name)
        self.reconstruction_weight = reconstruction_weight
        self.smoothness_weight = smoothness_weight
    
    def call(self, y_true, y_pred):
        # 1. 重建损失 (Huber损失)
        reconstruction_loss = tf.keras.losses.huber(y_true, y_pred)
        
        # 2. 平滑性损失 (相邻点差分的L2范数)
        # 计算一阶差分
        diff_true = y_true[:, 1:] - y_true[:, :-1]
        diff_pred = y_pred[:, 1:] - y_pred[:, :-1]
        smoothness_loss = tf.reduce_mean(tf.square(diff_pred - diff_true))
        
        # 3. 组合损失
        total_loss = (self.reconstruction_weight * reconstruction_loss + 
                     self.smoothness_weight * smoothness_loss)
        
        return total_loss
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "reconstruction_weight": self.reconstruction_weight,
            "smoothness_weight": self.smoothness_weight
        })
        return config

# 复用之前的注意力门控和残差块
class AttentionGate(layers.Layer):
    def __init__(self, filters, **kwargs):
        super(AttentionGate, self).__init__(**kwargs)
        self.filters = filters
        self.W_g = layers.Dense(filters, activation='relu')
        self.W_x = layers.Dense(filters, activation='relu')
        self.psi = layers.Dense(1, activation='sigmoid')
        
    def call(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.psi(layers.Add()([g1, x1]))
        return layers.Multiply()([x, psi])
    
    def get_config(self):
        config = super().get_config()
        config.update({"filters": self.filters})
        return config

class ResidualBlock(layers.Layer):
    def __init__(self, filters, dropout_rate=0.1, **kwargs):
        super(ResidualBlock, self).__init__(**kwargs)
        self.filters = filters
        self.dropout_rate = dropout_rate
        
        self.dense1 = layers.Dense(filters, activation='relu')
        self.bn1 = layers.BatchNormalization()
        self.dropout1 = layers.Dropout(dropout_rate)
        
        self.dense2 = layers.Dense(filters, activation='relu')
        self.bn2 = layers.BatchNormalization()
        self.dropout2 = layers.Dropout(dropout_rate)
        
        self.shortcut = layers.Dense(filters, activation='linear')
        
    def call(self, inputs, training=None):
        x = self.dense1(inputs)
        x = self.bn1(x, training=training)
        x = self.dropout1(x, training=training)
        
        x = self.dense2(x)
        x = self.bn2(x, training=training)
        x = self.dropout2(x, training=training)
        
        shortcut = self.shortcut(inputs)
        return layers.Add()([x, shortcut])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "dropout_rate": self.dropout_rate
        })
        return config

class ProgressiveImprovementV1:
    """
    渐进式改进V1: 只改进损失函数
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.history = None
        
        print(f"渐进式改进V1 - 平滑性损失函数:")
        print(f"- 分段长度: {segment_length} 样本点")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 改进点: 添加平滑性损失约束")
    
    def load_and_preprocess_data(self, dataset_path='epilepsy_wavelet_dataset'):
        """加载并预处理数据（保持不变）"""
        print("加载Gamma波数据...")
        
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        processed_waves = []
        
        for i, wave in enumerate(gamma_waves):
            # 基本预处理（保持原有逻辑）
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            processed_waves.append(wave_normalized)
        
        processed_waves = np.array(processed_waves)
        
        print(f"预处理完成: {processed_waves.shape}")
        return processed_waves, metadata
    
    def create_segments(self, processed_waves):
        """创建数据段（保持不变）"""
        print("创建数据段...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    if np.std(segment) > 0.01:
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        # 全局标准化
        self.scaler = RobustScaler()
        segments_reshaped = segments.reshape(-1, 1)
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        segments_final = segments_scaled.reshape(segments.shape)
        
        print(f"段创建完成: {len(segments_final)} 个段")
        
        self.segments = segments_final
        self.segment_info = segment_info
        
        return segments_final, segment_info
    
    def build_model_with_smooth_loss(self, input_dim=512, encoding_dim=32, base_filters=64):
        """
        构建带平滑性损失的模型（架构保持不变，只改损失函数）
        """
        print("构建带平滑性损失的自动编码器...")
        
        # ==================== 架构完全相同 ====================
        input_layer = keras.Input(shape=(input_dim,), name='input')
        
        # 编码器
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(input_layer)
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)
        
        # 瓶颈层
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)
        
        # 解码器
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)
        
        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)
        
        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)
        
        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)
        
        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)
        
        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)
        
        output = layers.Dense(input_dim, activation='linear', name='output')(dec3_skip)
        
        # ==================== 关键改进：使用平滑性损失 ====================
        autoencoder = keras.Model(input_layer, output, name='smooth_autoencoder_v1')
        encoder = keras.Model(input_layer, bottleneck, name='smooth_encoder_v1')
        
        # 使用自定义平滑性损失函数
        smooth_loss = SmoothLoss(reconstruction_weight=1.0, smoothness_weight=0.1)
        
        optimizer = keras.optimizers.Adam(learning_rate=0.0005)
        
        autoencoder.compile(
            optimizer=optimizer,
            loss=smooth_loss,  # 关键改进点
            metrics=['mae', 'mse']
        )
        
        print("平滑性损失模型构建完成:")
        print(f"- 架构: 完全相同")
        print(f"- 损失函数: Huber + 平滑性约束")
        print(f"- 平滑性权重: 0.1")
        print(f"- 总参数: {autoencoder.count_params():,}")
        
        self.autoencoder = autoencoder
        self.encoder = encoder
        
        return autoencoder, encoder

    def train_and_compare(self, X_train, X_test, epochs=50):
        """
        训练模型并与原版本对比
        """
        print("开始训练平滑性损失模型...")

        # 训练回调
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=15,
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=8,
                min_lr=1e-6,
                verbose=1
            )
        ]

        # 训练
        history = self.autoencoder.fit(
            X_train, X_train,
            validation_split=0.2,
            epochs=epochs,
            batch_size=32,
            callbacks=callbacks,
            verbose=1
        )

        self.history = history

        # 评估和对比
        print("\n评估平滑性改进效果...")
        X_reconstructed = self.autoencoder.predict(X_test, verbose=0)

        # 计算评估指标
        mse_errors = np.mean((X_test - X_reconstructed) ** 2, axis=1)
        mae_errors = np.mean(np.abs(X_test - X_reconstructed), axis=1)

        # 计算平滑性指标
        smoothness_original = self.calculate_smoothness(X_test)
        smoothness_reconstructed = self.calculate_smoothness(X_reconstructed)

        # 相关系数
        correlations = []
        for i in range(len(X_test)):
            corr = np.corrcoef(X_test[i], X_reconstructed[i])[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        correlations = np.array(correlations)

        print(f"\n平滑性改进V1结果:")
        print(f"- MSE误差: {np.mean(mse_errors):.4f} ± {np.std(mse_errors):.4f}")
        print(f"- MAE误差: {np.mean(mae_errors):.4f} ± {np.std(mae_errors):.4f}")
        print(f"- 相关系数: {np.mean(correlations):.4f} ± {np.std(correlations):.4f}")
        print(f"- 原始信号平滑度: {np.mean(smoothness_original):.4f}")
        print(f"- 重建信号平滑度: {np.mean(smoothness_reconstructed):.4f}")
        print(f"- 平滑度比值: {np.mean(smoothness_reconstructed)/np.mean(smoothness_original):.4f}")

        return {
            'mse_errors': mse_errors,
            'mae_errors': mae_errors,
            'correlations': correlations,
            'smoothness_original': smoothness_original,
            'smoothness_reconstructed': smoothness_reconstructed,
            'reconstructed': X_reconstructed
        }

    def calculate_smoothness(self, signals):
        """
        计算信号平滑度（一阶差分的方差，越小越平滑）
        """
        smoothness_scores = []
        for signal in signals:
            diff = np.diff(signal)
            smoothness = np.var(diff)
            smoothness_scores.append(smoothness)
        return np.array(smoothness_scores)

    def visualize_comparison(self, X_test, results, n_samples=3):
        """
        可视化对比结果
        """
        print("创建对比可视化...")

        X_reconstructed = results['reconstructed']
        correlations = results['correlations']

        fig, axes = plt.subplots(n_samples, 1, figsize=(15, 4*n_samples))
        if n_samples == 1:
            axes = [axes]

        time = np.arange(512) / 128

        # 选择不同质量的重建样本
        best_idx = np.argmax(correlations)
        worst_idx = np.argmin(correlations)
        median_idx = np.argsort(correlations)[len(correlations)//2]

        samples = [
            (best_idx, f'最佳重建 (相关系数: {correlations[best_idx]:.3f})', 'green'),
            (median_idx, f'中等重建 (相关系数: {correlations[median_idx]:.3f})', 'blue'),
            (worst_idx, f'最差重建 (相关系数: {correlations[worst_idx]:.3f})', 'red')
        ]

        for i, (idx, title, color) in enumerate(samples):
            # 计算平滑度
            smooth_orig = self.calculate_smoothness([X_test[idx]])[0]
            smooth_recon = self.calculate_smoothness([X_reconstructed[idx]])[0]

            axes[i].plot(time, X_test[idx], 'k-', label='原始信号', linewidth=2, alpha=0.8)
            axes[i].plot(time, X_reconstructed[idx], '--', color=color, label='重建信号', linewidth=2)

            title_with_smooth = f'{title}\n平滑度: 原始={smooth_orig:.4f}, 重建={smooth_recon:.4f}'
            axes[i].set_title(title_with_smooth, fontsize=11, fontweight='bold')
            axes[i].set_xlabel('时间 (秒)')
            axes[i].set_ylabel('标准化幅值')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('平滑性改进V1对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("对比可视化完成!")


def test_v1_improvement():
    """
    测试V1改进效果
    """
    print("=" * 60)
    print("渐进式改进V1测试: 平滑性损失函数")
    print("=" * 60)
    
    # 初始化
    pipeline = ProgressiveImprovementV1()
    
    # 加载数据
    processed_waves, metadata = pipeline.load_and_preprocess_data()
    segments, segment_info = pipeline.create_segments(processed_waves)
    
    # 划分数据
    X_train, X_test = train_test_split(segments, test_size=0.2, random_state=42)
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")
    
    # 构建模型
    autoencoder, encoder = pipeline.build_model_with_smooth_loss()

    print("\n模型结构:")
    autoencoder.summary()

    # 训练和评估
    print("\n开始训练和评估...")
    results = pipeline.train_and_compare(X_train, X_test, epochs=50)

    # 可视化对比
    pipeline.visualize_comparison(X_test, results, n_samples=3)

    # 保存模型（如果效果好）
    print("\n保存平滑性改进V1模型...")
    os.makedirs('progressive_models_v1', exist_ok=True)
    pipeline.autoencoder.save('progressive_models_v1/smooth_loss_autoencoder_v1.keras')

    import joblib
    joblib.dump(pipeline.scaler, 'progressive_models_v1/smooth_loss_scaler_v1.pkl')

    print("\n=" * 60)
    print("渐进式改进V1完成!")
    print("=" * 60)
    print("改进总结:")
    print("- ✅ 添加了平滑性损失约束")
    print("- ✅ 保持原有架构不变")
    print("- ✅ 专门优化信号平滑度")
    print("- ✅ 量化评估平滑度改进")

    # 决策建议
    smooth_ratio = np.mean(results['smoothness_reconstructed']) / np.mean(results['smoothness_original'])
    avg_correlation = np.mean(results['correlations'])

    print(f"\n效果评估:")
    print(f"- 平滑度比值: {smooth_ratio:.3f} (越接近1.0越好)")
    print(f"- 平均相关系数: {avg_correlation:.3f} (越接近1.0越好)")

    if smooth_ratio < 2.0 and avg_correlation > 0.8:
        print("✅ 改进效果良好，建议保留此改进")
        recommendation = "KEEP"
    elif smooth_ratio < 3.0 and avg_correlation > 0.7:
        print("⚠️  改进效果一般，可以考虑调整参数")
        recommendation = "ADJUST"
    else:
        print("❌ 改进效果不佳，建议尝试其他方法")
        recommendation = "DISCARD"

    return pipeline, X_train, X_test, results, recommendation


if __name__ == "__main__":
    pipeline, X_train, X_test, results, recommendation = test_v1_improvement()

    print(f"\n最终建议: {recommendation}")

    if recommendation == "KEEP":
        print("🎉 V1改进成功！可以进行下一步改进。")
    elif recommendation == "ADJUST":
        print("🔧 V1需要调整，建议修改平滑性权重。")
    else:
        print("🔄 V1效果不佳，需要尝试其他改进方案。")
