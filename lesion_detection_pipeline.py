#!/usr/bin/env python3
"""
病灶检测管道 - 基于V2自动编码器
Lesion Detection Pipeline - Based on V2 Autoencoder

流程:
1. 用控制组显著通道某波样本训练V2模型 (200轮)
2. 用训练好的模型重建癫痫组数据
3. 通过重建误差识别病灶信号
4. 生成病灶热图和分析报告
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
from sklearn.preprocessing import RobustScaler
from sklearn.model_selection import train_test_split
import warnings
import os
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 导入V2模型的核心组件
class ImprovedSmoothLoss(keras.losses.Loss):
    """改进的平滑性损失函数"""
    def __init__(self, reconstruction_weight=1.0, smoothness_weight=0.05, 
                 curvature_weight=0.02, name="improved_smooth_loss"):
        super().__init__(name=name)
        self.reconstruction_weight = reconstruction_weight
        self.smoothness_weight = smoothness_weight
        self.curvature_weight = curvature_weight
    
    def call(self, y_true, y_pred):
        reconstruction_loss = tf.keras.losses.huber(y_true, y_pred)
        
        diff1_true = y_true[:, 1:] - y_true[:, :-1]
        diff1_pred = y_pred[:, 1:] - y_pred[:, :-1]
        smoothness_loss = tf.reduce_mean(tf.square(diff1_pred - diff1_true))
        
        diff2_true = diff1_true[:, 1:] - diff1_true[:, :-1]
        diff2_pred = diff1_pred[:, 1:] - diff1_pred[:, :-1]
        curvature_loss = tf.reduce_mean(tf.square(diff2_pred - diff2_true))
        
        total_loss = (self.reconstruction_weight * reconstruction_loss + 
                     self.smoothness_weight * smoothness_loss +
                     self.curvature_weight * curvature_loss)
        
        return total_loss
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "reconstruction_weight": self.reconstruction_weight,
            "smoothness_weight": self.smoothness_weight,
            "curvature_weight": self.curvature_weight
        })
        return config

class AttentionGate(layers.Layer):
    def __init__(self, filters, **kwargs):
        super(AttentionGate, self).__init__(**kwargs)
        self.filters = filters
        self.W_g = layers.Dense(filters, activation='relu')
        self.W_x = layers.Dense(filters, activation='relu')
        self.psi = layers.Dense(1, activation='sigmoid')
        
    def call(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.psi(layers.Add()([g1, x1]))
        return layers.Multiply()([x, psi])
    
    def get_config(self):
        config = super().get_config()
        config.update({"filters": self.filters})
        return config

class ResidualBlock(layers.Layer):
    def __init__(self, filters, dropout_rate=0.1, **kwargs):
        super(ResidualBlock, self).__init__(**kwargs)
        self.filters = filters
        self.dropout_rate = dropout_rate
        
        self.dense1 = layers.Dense(filters, activation='relu')
        self.bn1 = layers.BatchNormalization()
        self.dropout1 = layers.Dropout(dropout_rate)
        
        self.dense2 = layers.Dense(filters, activation='relu')
        self.bn2 = layers.BatchNormalization()
        self.dropout2 = layers.Dropout(dropout_rate)
        
        self.shortcut = layers.Dense(filters, activation='linear')
        
    def call(self, inputs, training=None):
        x = self.dense1(inputs)
        x = self.bn1(x, training=training)
        x = self.dropout1(x, training=training)
        
        x = self.dense2(x)
        x = self.bn2(x, training=training)
        x = self.dropout2(x, training=training)
        
        shortcut = self.shortcut(inputs)
        return layers.Add()([x, shortcut])
    
    def get_config(self):
        config = super().get_config()
        config.update({
            "filters": self.filters,
            "dropout_rate": self.dropout_rate
        })
        return config

class LesionDetectionPipeline:
    """
    病灶检测管道
    """
    
    def __init__(self, segment_length=512, overlap_ratio=0.75, sampling_rate=128):
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        # 模型组件
        self.autoencoder = None
        self.encoder = None
        self.scaler = None
        self.history = None
        
        # 数据存储
        self.control_segments = None
        self.epilepsy_segments = None
        self.control_metadata = None
        self.epilepsy_metadata = None
        
        print(f"病灶检测管道初始化:")
        print(f"- 分段长度: {segment_length} 样本点 ({segment_length/sampling_rate:.1f}秒)")
        print(f"- 重叠比例: {overlap_ratio*100:.0f}%")
        print(f"- 训练轮数: 200轮")
    
    def load_control_group_data(self, wave_type='gamma'):
        """
        加载控制组显著通道某波样本
        """
        print(f"加载控制组{wave_type}波数据...")
        
        # 从clinical_eeg_preprocessing.py的结果加载
        try:
            # 加载预处理后的控制组数据
            control_data_path = f'control_group_{wave_type}_waves.npy'
            control_metadata_path = f'control_group_{wave_type}_metadata.csv'
            
            if os.path.exists(control_data_path):
                control_waves = np.load(control_data_path)
                control_metadata = pd.read_csv(control_metadata_path)
                print(f"从文件加载控制组数据: {control_waves.shape}")
            else:
                # 如果没有预处理文件，使用默认数据
                print("未找到控制组预处理文件，使用默认数据...")
                control_waves = np.load('epilepsy_wavelet_dataset/epilepsy_gamma_waves.npy')
                control_metadata = pd.read_csv('epilepsy_wavelet_dataset/epilepsy_metadata.csv')
                
                # 模拟控制组（取前一半作为控制组）
                n_control = len(control_waves) // 2
                control_waves = control_waves[:n_control]
                control_metadata = control_metadata.iloc[:n_control].copy()
                control_metadata['group'] = 'control'
                
                print(f"模拟控制组数据: {control_waves.shape}")
        
        except Exception as e:
            print(f"加载数据时出错: {e}")
            print("使用默认数据集...")
            control_waves = np.load('epilepsy_wavelet_dataset/epilepsy_gamma_waves.npy')
            control_metadata = pd.read_csv('epilepsy_wavelet_dataset/epilepsy_metadata.csv')
            
            # 模拟控制组
            n_control = len(control_waves) // 2
            control_waves = control_waves[:n_control]
            control_metadata = control_metadata.iloc[:n_control].copy()
            control_metadata['group'] = 'control'
        
        # 预处理控制组数据
        processed_control = self.preprocess_waves(control_waves)
        
        # 创建数据段
        control_segments, control_segment_info = self.create_segments(processed_control)
        
        self.control_segments = control_segments
        self.control_metadata = control_metadata
        
        print(f"控制组数据准备完成:")
        print(f"- 原始样本: {len(control_waves)} 个")
        print(f"- 数据段: {len(control_segments)} 个")
        print(f"- 数据范围: [{control_segments.min():.3f}, {control_segments.max():.3f}]")
        
        return control_segments, control_metadata
    
    def load_epilepsy_group_data(self, wave_type='gamma'):
        """
        加载癫痫组数据
        """
        print(f"加载癫痫组{wave_type}波数据...")
        
        try:
            # 尝试加载癫痫组数据
            epilepsy_data_path = f'epilepsy_group_{wave_type}_waves.npy'
            epilepsy_metadata_path = f'epilepsy_group_{wave_type}_metadata.csv'
            
            if os.path.exists(epilepsy_data_path):
                epilepsy_waves = np.load(epilepsy_data_path)
                epilepsy_metadata = pd.read_csv(epilepsy_metadata_path)
                print(f"从文件加载癫痫组数据: {epilepsy_waves.shape}")
            else:
                # 使用默认数据的后一半作为癫痫组
                print("未找到癫痫组预处理文件，使用默认数据...")
                all_waves = np.load('epilepsy_wavelet_dataset/epilepsy_gamma_waves.npy')
                all_metadata = pd.read_csv('epilepsy_wavelet_dataset/epilepsy_metadata.csv')
                
                n_control = len(all_waves) // 2
                epilepsy_waves = all_waves[n_control:]
                epilepsy_metadata = all_metadata.iloc[n_control:].copy()
                epilepsy_metadata['group'] = 'epilepsy'
                
                print(f"模拟癫痫组数据: {epilepsy_waves.shape}")
        
        except Exception as e:
            print(f"加载癫痫组数据时出错: {e}")
            return None, None
        
        # 预处理癫痫组数据
        processed_epilepsy = self.preprocess_waves(epilepsy_waves)
        
        # 创建数据段
        epilepsy_segments, epilepsy_segment_info = self.create_segments(processed_epilepsy)
        
        self.epilepsy_segments = epilepsy_segments
        self.epilepsy_metadata = epilepsy_metadata
        
        print(f"癫痫组数据准备完成:")
        print(f"- 原始样本: {len(epilepsy_waves)} 个")
        print(f"- 数据段: {len(epilepsy_segments)} 个")
        print(f"- 数据范围: [{epilepsy_segments.min():.3f}, {epilepsy_segments.max():.3f}]")
        
        return epilepsy_segments, epilepsy_metadata
    
    def preprocess_waves(self, waves):
        """预处理波形数据"""
        processed_waves = []
        
        for wave in waves:
            # 去除异常值
            mean_val = np.mean(wave)
            std_val = np.std(wave)
            wave_clean = np.clip(wave, mean_val - 3*std_val, mean_val + 3*std_val)
            
            # 去趋势
            x = np.arange(len(wave_clean))
            coeffs = np.polyfit(x, wave_clean, 1)
            trend = np.polyval(coeffs, x)
            wave_detrend = wave_clean - trend
            
            # 带通滤波
            from scipy import signal
            nyquist = self.sampling_rate / 2
            low_freq = 25 / nyquist
            high_freq = 70 / nyquist
            
            if low_freq < 1 and high_freq < 1:
                b, a = signal.butter(4, [low_freq, high_freq], btype='band')
                wave_filtered = signal.filtfilt(b, a, wave_detrend)
            else:
                wave_filtered = wave_detrend
            
            # 标准化
            wave_normalized = (wave_filtered - np.mean(wave_filtered)) / (np.std(wave_filtered) + 1e-8)
            processed_waves.append(wave_normalized)
        
        return np.array(processed_waves)
    
    def create_segments(self, processed_waves):
        """创建数据段"""
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(processed_waves):
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    
                    if np.std(segment) > 0.01:
                        all_segments.append(segment)
                        segment_info.append({
                            'sample_idx': sample_idx,
                            'segment_idx': seg_idx,
                            'start_idx': start_idx,
                            'end_idx': end_idx
                        })
        
        segments = np.array(all_segments)
        
        # 全局标准化
        if self.scaler is None:
            self.scaler = RobustScaler()
            segments_reshaped = segments.reshape(-1, 1)
            segments_scaled = self.scaler.fit_transform(segments_reshaped)
        else:
            segments_reshaped = segments.reshape(-1, 1)
            segments_scaled = self.scaler.transform(segments_reshaped)
        
        segments_final = segments_scaled.reshape(segments.shape)
        
        return segments_final, segment_info

    def build_v2_autoencoder(self, input_dim=512, encoding_dim=64, base_filters=64):
        """
        构建V2自动编码器 (与progressive_improvement_v2.py完全相同)
        """
        print("构建V2自动编码器用于病灶检测...")

        input_layer = keras.Input(shape=(input_dim,), name='input')

        # 编码器
        enc1 = ResidualBlock(base_filters*4, name='encoder_block1')(input_layer)
        enc2 = ResidualBlock(base_filters*2, name='encoder_block2')(enc1)
        enc3 = ResidualBlock(base_filters, name='encoder_block3')(enc2)

        # 瓶颈层 - 编码维度64
        bottleneck = layers.Dense(encoding_dim, activation='relu', name='bottleneck')(enc3)
        bottleneck = layers.BatchNormalization(name='bottleneck_bn')(bottleneck)
        bottleneck = layers.Dropout(0.3, name='bottleneck_dropout')(bottleneck)

        # 解码器
        dec1 = layers.Dense(base_filters, activation='relu', name='decoder_dense1')(bottleneck)
        dec1 = layers.BatchNormalization(name='decoder_bn1')(dec1)
        dec1 = layers.Dropout(0.2, name='decoder_dropout1')(dec1)

        skip1 = AttentionGate(base_filters, name='attention_gate1')(dec1, enc3)
        dec1_skip = layers.Add(name='skip_connection1')([dec1, skip1])
        dec1_skip = ResidualBlock(base_filters, name='decoder_block1')(dec1_skip)

        dec2 = layers.Dense(base_filters*2, activation='relu', name='decoder_dense2')(dec1_skip)
        dec2 = layers.BatchNormalization(name='decoder_bn2')(dec2)
        dec2 = layers.Dropout(0.2, name='decoder_dropout2')(dec2)

        skip2 = AttentionGate(base_filters*2, name='attention_gate2')(dec2, enc2)
        dec2_skip = layers.Add(name='skip_connection2')([dec2, skip2])
        dec2_skip = ResidualBlock(base_filters*2, name='decoder_block2')(dec2_skip)

        dec3 = layers.Dense(base_filters*4, activation='relu', name='decoder_dense3')(dec2_skip)
        dec3 = layers.BatchNormalization(name='decoder_bn3')(dec3)
        dec3 = layers.Dropout(0.2, name='decoder_dropout3')(dec3)

        skip3 = AttentionGate(base_filters*4, name='attention_gate3')(dec3, enc1)
        dec3_skip = layers.Add(name='skip_connection3')([dec3, skip3])
        dec3_skip = ResidualBlock(base_filters*4, name='decoder_block3')(dec3_skip)

        output = layers.Dense(input_dim, activation='linear', name='output')(dec3_skip)

        # 构建模型
        autoencoder = keras.Model(input_layer, output, name='lesion_detection_autoencoder_v2')
        encoder = keras.Model(input_layer, bottleneck, name='lesion_detection_encoder_v2')

        # 使用改进的平滑性损失函数
        improved_smooth_loss = ImprovedSmoothLoss(
            reconstruction_weight=1.0,
            smoothness_weight=0.05,
            curvature_weight=0.02
        )

        optimizer = keras.optimizers.Adam(learning_rate=0.0005)

        autoencoder.compile(
            optimizer=optimizer,
            loss=improved_smooth_loss,
            metrics=['mae', 'mse']
        )

        print("V2自动编码器构建完成:")
        print(f"- 编码维度: {encoding_dim}")
        print(f"- 总参数: {autoencoder.count_params():,}")

        self.autoencoder = autoencoder
        self.encoder = encoder

        return autoencoder, encoder

    def train_on_control_group(self, control_segments, epochs=200, batch_size=32):
        """
        用控制组数据训练模型 (200轮)
        """
        print(f"开始用控制组数据训练模型 ({epochs}轮)...")

        # 划分训练和验证集
        X_train, X_val = train_test_split(control_segments, test_size=0.2, random_state=42)

        print(f"控制组数据划分:")
        print(f"- 训练集: {X_train.shape[0]} 个段")
        print(f"- 验证集: {X_val.shape[0]} 个段")

        # 训练回调
        callbacks = [
            keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=30,  # 增加耐心值，因为要训练200轮
                restore_best_weights=True,
                verbose=1
            ),
            keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',
                factor=0.7,
                patience=15,
                min_lr=1e-7,
                verbose=1
            ),
            keras.callbacks.ModelCheckpoint(
                'lesion_detection_best_model.keras',
                monitor='val_loss',
                save_best_only=True,
                verbose=1
            )
        ]

        # 训练模型
        print(f"开始训练 - 目标轮数: {epochs}")
        history = self.autoencoder.fit(
            X_train, X_train,
            validation_data=(X_val, X_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=1
        )

        self.history = history

        print("控制组模型训练完成!")
        print(f"- 实际训练轮数: {len(history.history['loss'])}")
        print(f"- 最终训练损失: {history.history['loss'][-1]:.4f}")
        print(f"- 最终验证损失: {history.history['val_loss'][-1]:.4f}")

        return history

    def detect_lesions_in_epilepsy_group(self, epilepsy_segments):
        """
        用训练好的模型检测癫痫组中的病灶
        """
        print("开始检测癫痫组中的病灶...")

        if self.autoencoder is None:
            print("❌ 模型未训练，无法进行病灶检测")
            return None

        # 重建癫痫组数据
        epilepsy_reconstructed = self.autoencoder.predict(epilepsy_segments, verbose=1)

        # 计算重建误差
        reconstruction_errors = np.mean((epilepsy_segments - epilepsy_reconstructed) ** 2, axis=1)

        # 计算异常阈值 (使用控制组的重建误差作为基准)
        print("计算异常检测阈值...")
        if hasattr(self, 'control_segments') and self.control_segments is not None:
            control_reconstructed = self.autoencoder.predict(self.control_segments, verbose=0)
            control_errors = np.mean((self.control_segments - control_reconstructed) ** 2, axis=1)

            # 使用控制组误差的95%分位数作为阈值
            threshold = np.percentile(control_errors, 95)
            print(f"控制组重建误差统计:")
            print(f"- 均值: {np.mean(control_errors):.4f}")
            print(f"- 标准差: {np.std(control_errors):.4f}")
            print(f"- 95%分位数阈值: {threshold:.4f}")
        else:
            # 如果没有控制组数据，使用癫痫组误差的75%分位数
            threshold = np.percentile(reconstruction_errors, 75)
            print(f"使用癫痫组75%分位数作为阈值: {threshold:.4f}")

        # 识别病灶
        lesion_mask = reconstruction_errors > threshold
        lesion_indices = np.where(lesion_mask)[0]

        print(f"病灶检测结果:")
        print(f"- 癫痫组总段数: {len(epilepsy_segments)}")
        print(f"- 检测到病灶段数: {len(lesion_indices)}")
        print(f"- 病灶比例: {len(lesion_indices)/len(epilepsy_segments)*100:.1f}%")
        print(f"- 重建误差范围: [{reconstruction_errors.min():.4f}, {reconstruction_errors.max():.4f}]")

        return {
            'reconstruction_errors': reconstruction_errors,
            'threshold': threshold,
            'lesion_mask': lesion_mask,
            'lesion_indices': lesion_indices,
            'reconstructed_signals': epilepsy_reconstructed
        }


def main():
    """
    主函数 - 病灶检测管道
    """
    print("=" * 70)
    print("病灶检测管道 - 基于V2自动编码器")
    print("=" * 70)
    
    # 初始化管道
    pipeline = LesionDetectionPipeline()
    
    # 步骤1: 加载控制组数据
    print("\n步骤1: 加载控制组显著通道某波样本")
    control_segments, control_metadata = pipeline.load_control_group_data('gamma')
    
    if control_segments is None:
        print("❌ 控制组数据加载失败")
        return
    
    # 步骤2: 加载癫痫组数据
    print("\n步骤2: 加载癫痫组数据")
    epilepsy_segments, epilepsy_metadata = pipeline.load_epilepsy_group_data('gamma')
    
    if epilepsy_segments is None:
        print("❌ 癫痫组数据加载失败")
        return
    
    print(f"\n数据加载完成:")
    print(f"- 控制组段数: {len(control_segments)}")
    print(f"- 癫痫组段数: {len(epilepsy_segments)}")

    # 步骤3: 构建V2自动编码器
    print("\n步骤3: 构建V2自动编码器")
    autoencoder, encoder = pipeline.build_v2_autoencoder(
        input_dim=512,
        encoding_dim=64,
        base_filters=64
    )

    # 步骤4: 用控制组数据训练模型200轮
    print("\n步骤4: 用控制组数据训练模型200轮")
    history = pipeline.train_on_control_group(control_segments, epochs=200, batch_size=32)

    # 步骤5: 检测癫痫组中的病灶
    print("\n步骤5: 检测癫痫组中的病灶")
    lesion_results = pipeline.detect_lesions_in_epilepsy_group(epilepsy_segments)

    # 步骤6: 生成病灶分析报告和可视化
    print("\n步骤6: 生成病灶分析报告和可视化")
    create_lesion_analysis_report(pipeline, lesion_results, history)

    # 步骤7: 保存模型和结果
    print("\n步骤7: 保存模型和结果")
    save_lesion_detection_results(pipeline, lesion_results)

    print("\n=" * 70)
    print("病灶检测管道完成!")
    print("=" * 70)
    print("主要输出:")
    print("- ✅ 训练好的V2自动编码器")
    print("- ✅ 病灶检测结果")
    print("- ✅ 病灶热图和分析报告")
    print("- ✅ 重建误差分析")

    return pipeline, lesion_results


def create_lesion_analysis_report(pipeline, lesion_results, history):
    """
    创建病灶分析报告和可视化
    """
    print("创建病灶分析报告...")

    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 1. 训练历史可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))

    # 损失曲线
    axes[0, 0].plot(history.history['loss'], 'b-', label='训练损失', linewidth=2)
    axes[0, 0].plot(history.history['val_loss'], 'r-', label='验证损失', linewidth=2)
    axes[0, 0].set_title('控制组训练损失曲线', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('训练轮数')
    axes[0, 0].set_ylabel('损失值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # MAE曲线
    axes[0, 1].plot(history.history['mae'], 'g-', label='训练MAE', linewidth=2)
    axes[0, 1].plot(history.history['val_mae'], 'orange', label='验证MAE', linewidth=2)
    axes[0, 1].set_title('平均绝对误差曲线', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('训练轮数')
    axes[0, 1].set_ylabel('MAE值')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)

    # 重建误差分布
    reconstruction_errors = lesion_results['reconstruction_errors']
    threshold = lesion_results['threshold']

    axes[1, 0].hist(reconstruction_errors, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 0].axvline(threshold, color='red', linestyle='--', linewidth=2, label=f'阈值: {threshold:.4f}')
    axes[1, 0].set_title('癫痫组重建误差分布', fontsize=14, fontweight='bold')
    axes[1, 0].set_xlabel('重建误差 (MSE)')
    axes[1, 0].set_ylabel('频次')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)

    # 病灶检测统计
    lesion_mask = lesion_results['lesion_mask']
    normal_count = np.sum(~lesion_mask)
    lesion_count = np.sum(lesion_mask)

    labels = ['正常段', '病灶段']
    sizes = [normal_count, lesion_count]
    colors = ['lightgreen', 'lightcoral']

    axes[1, 1].pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[1, 1].set_title('病灶检测结果统计', fontsize=14, fontweight='bold')

    plt.tight_layout()
    plt.savefig('病灶检测分析报告.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 病灶信号对比可视化
    fig, axes = plt.subplots(3, 1, figsize=(16, 12))

    time = np.arange(512) / 128  # 4秒时间轴
    epilepsy_segments = pipeline.epilepsy_segments
    reconstructed_signals = lesion_results['reconstructed_signals']
    lesion_indices = lesion_results['lesion_indices']

    # 选择最严重的病灶样本
    if len(lesion_indices) > 0:
        lesion_errors = reconstruction_errors[lesion_indices]
        worst_lesion_idx = lesion_indices[np.argmax(lesion_errors)]
        moderate_lesion_idx = lesion_indices[len(lesion_indices)//2] if len(lesion_indices) > 1 else worst_lesion_idx
        mild_lesion_idx = lesion_indices[np.argmin(lesion_errors)]

        samples = [
            (worst_lesion_idx, f'严重病灶 (误差: {reconstruction_errors[worst_lesion_idx]:.4f})', 'red'),
            (moderate_lesion_idx, f'中等病灶 (误差: {reconstruction_errors[moderate_lesion_idx]:.4f})', 'orange'),
            (mild_lesion_idx, f'轻微病灶 (误差: {reconstruction_errors[mild_lesion_idx]:.4f})', 'yellow')
        ]

        for i, (idx, title, color) in enumerate(samples):
            axes[i].plot(time, epilepsy_segments[idx], 'k-', label='原始癫痫信号', linewidth=2, alpha=0.8)
            axes[i].plot(time, reconstructed_signals[idx], '--', color=color, label='重建信号', linewidth=2)
            axes[i].fill_between(time, epilepsy_segments[idx], reconstructed_signals[idx],
                               alpha=0.3, color=color, label='重建误差区域')
            axes[i].set_title(title, fontsize=12, fontweight='bold')
            axes[i].set_xlabel('时间 (秒)')
            axes[i].set_ylabel('标准化幅值')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
    else:
        axes[1].text(0.5, 0.5, '未检测到明显病灶', ha='center', va='center',
                    transform=axes[1].transAxes, fontsize=16)

    plt.tight_layout()
    plt.savefig('病灶信号对比分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 病灶热图
    create_lesion_heatmap(lesion_results)

    print("病灶分析报告创建完成!")


def create_lesion_heatmap(lesion_results):
    """
    创建病灶热图
    """
    print("创建病灶热图...")

    reconstruction_errors = lesion_results['reconstruction_errors']
    lesion_mask = lesion_results['lesion_mask']

    # 重塑为2D热图格式
    n_segments = len(reconstruction_errors)
    grid_size = int(np.ceil(np.sqrt(n_segments)))

    # 创建热图数据
    heatmap_data = np.zeros((grid_size, grid_size))
    for i, error in enumerate(reconstruction_errors):
        row = i // grid_size
        col = i % grid_size
        if row < grid_size and col < grid_size:
            heatmap_data[row, col] = error

    # 绘制热图
    plt.figure(figsize=(12, 10))

    # 使用自定义颜色映射
    cmap = plt.cm.Reds
    im = plt.imshow(heatmap_data, cmap=cmap, aspect='auto')

    plt.title('癫痫组病灶检测热图\n(颜色越深表示重建误差越大，越可能是病灶)',
              fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('段索引 (列)', fontsize=12)
    plt.ylabel('段索引 (行)', fontsize=12)

    # 添加颜色条
    cbar = plt.colorbar(im, shrink=0.8)
    cbar.set_label('重建误差 (MSE)', fontsize=12)

    # 标记阈值线
    threshold = lesion_results['threshold']
    cbar.ax.axhline(threshold, color='blue', linestyle='--', linewidth=2)
    cbar.ax.text(0.5, threshold, f'阈值: {threshold:.4f}',
                transform=cbar.ax.get_yaxis_transform(),
                ha='left', va='bottom', color='blue', fontweight='bold')

    plt.tight_layout()
    plt.savefig('病灶检测热图.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("病灶热图创建完成!")


def save_lesion_detection_results(pipeline, lesion_results):
    """
    保存病灶检测结果
    """
    print("保存病灶检测结果...")

    # 创建结果目录
    os.makedirs('lesion_detection_results', exist_ok=True)

    # 保存模型
    pipeline.autoencoder.save('lesion_detection_results/lesion_detection_autoencoder_v2.keras')
    pipeline.encoder.save('lesion_detection_results/lesion_detection_encoder_v2.keras')

    # 保存标准化器
    import joblib
    joblib.dump(pipeline.scaler, 'lesion_detection_results/lesion_detection_scaler.pkl')

    # 保存检测结果
    np.save('lesion_detection_results/reconstruction_errors.npy', lesion_results['reconstruction_errors'])
    np.save('lesion_detection_results/lesion_mask.npy', lesion_results['lesion_mask'])
    np.save('lesion_detection_results/lesion_indices.npy', lesion_results['lesion_indices'])
    np.save('lesion_detection_results/reconstructed_signals.npy', lesion_results['reconstructed_signals'])

    # 保存检测报告
    report = {
        'total_segments': len(lesion_results['reconstruction_errors']),
        'lesion_segments': len(lesion_results['lesion_indices']),
        'lesion_ratio': len(lesion_results['lesion_indices']) / len(lesion_results['reconstruction_errors']),
        'threshold': lesion_results['threshold'],
        'error_stats': {
            'mean': float(np.mean(lesion_results['reconstruction_errors'])),
            'std': float(np.std(lesion_results['reconstruction_errors'])),
            'min': float(np.min(lesion_results['reconstruction_errors'])),
            'max': float(np.max(lesion_results['reconstruction_errors']))
        }
    }

    import json
    with open('lesion_detection_results/detection_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print("病灶检测结果保存完成!")
    print("保存的文件:")
    print("- lesion_detection_autoencoder_v2.keras (训练好的自动编码器)")
    print("- lesion_detection_encoder_v2.keras (编码器)")
    print("- lesion_detection_scaler.pkl (数据标准化器)")
    print("- reconstruction_errors.npy (重建误差)")
    print("- lesion_mask.npy (病灶掩码)")
    print("- lesion_indices.npy (病灶索引)")
    print("- reconstructed_signals.npy (重建信号)")
    print("- detection_report.json (检测报告)")


if __name__ == "__main__":
    pipeline, lesion_results = main()
