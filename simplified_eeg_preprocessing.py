#!/usr/bin/env python3
"""
Simplified Clinical EEG Preprocessing Pipeline
Fast preprocessing for demonstration purposes
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mne
from mne.preprocessing import ICA
import gzip
import os
import warnings
from scipy import signal
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Suppress warnings
warnings.filterwarnings('ignore', category=RuntimeWarning)
mne.set_log_level('WARNING')

class SimplifiedEEGPreprocessor:
    """
    Simplified EEG preprocessing pipeline for demonstration
    """
    
    def __init__(self, sfreq=128):
        self.sfreq = sfreq
        self.eeg_channels = ['AF3', 'AF4', 'F3', 'F4', 'F7', 'F8', 'FC5', 'FC6', 
                           'O1', 'O2', 'P7', 'P8', 'T7', 'T8']
        
    def load_and_preprocess(self, file_path, dataset_type='nigeria'):
        """
        Load and apply basic preprocessing
        """
        try:
            logger.info(f"Processing {file_path}")
            
            # Load data
            with gzip.open(file_path, 'rt') as f:
                if dataset_type == 'nigeria':
                    df = pd.read_csv(f)
                    eeg_data = df[self.eeg_channels].apply(pd.to_numeric, errors='coerce').values.T
                    ch_names = self.eeg_channels
                else:
                    df = pd.read_csv(f, header=None)
                    eeg_data = pd.to_numeric(df.iloc[:, 1:15].values.flatten(), 
                                           errors='coerce').reshape(df.shape[0], 14).T
                    ch_names = [f'EEG_{i+1:02d}' for i in range(14)]
            
            # Clean data
            eeg_data = np.nan_to_num(eeg_data, nan=0.0).astype(float) * 1e-6
            
            # Create MNE Raw object
            info = mne.create_info(ch_names=ch_names, sfreq=self.sfreq, ch_types='eeg')
            raw_original = mne.io.RawArray(eeg_data, info)
            
            if dataset_type == 'nigeria':
                montage = mne.channels.make_standard_montage('standard_1020')
                raw_original.set_montage(montage, on_missing='ignore')
            
            # Apply basic preprocessing
            raw_preprocessed = raw_original.copy()
            
            # 1. Filtering
            raw_preprocessed.filter(l_freq=0.5, h_freq=60, verbose=False)
            raw_preprocessed.notch_filter(freqs=50, verbose=False)
            
            # 2. Re-referencing
            raw_preprocessed.set_eeg_reference('average', projection=True, verbose=False)
            raw_preprocessed.apply_proj(verbose=False)
            
            # 3. Simple ICA (limited components for speed)
            epochs = mne.make_fixed_length_epochs(raw_preprocessed, duration=2.0, preload=True, verbose=False)
            
            if len(epochs) > 10:  # Only apply ICA if enough epochs
                ica = ICA(n_components=5, method='picard', random_state=42, verbose=False)
                ica.fit(epochs, verbose=False)
                
                # Simple artifact detection based on variance
                ica_data = ica.get_sources(epochs).get_data()
                component_vars = np.var(ica_data, axis=(0, 2))
                
                # Remove components with highest variance (likely artifacts)
                if len(component_vars) > 2:
                    artifact_idx = np.argsort(component_vars)[-2:]  # Remove top 2 high-variance components
                    ica.exclude = artifact_idx.tolist()
                    epochs_clean = ica.apply(epochs.copy(), verbose=False)
                else:
                    epochs_clean = epochs
                
                # Convert back to continuous
                raw_preprocessed = epochs_clean.to_data_frame().reset_index()
                eeg_columns = [col for col in raw_preprocessed.columns if col in ch_names]
                final_data = raw_preprocessed[eeg_columns].values.T
                
                info_final = mne.create_info(ch_names=eeg_columns, sfreq=self.sfreq, ch_types='eeg')
                raw_preprocessed = mne.io.RawArray(final_data, info_final)
                
                if dataset_type == 'nigeria':
                    raw_preprocessed.set_montage(montage, on_missing='ignore')
            
            # Calculate quality metrics
            data_orig = raw_original.get_data()
            data_prep = raw_preprocessed.get_data()
            
            # Ensure same length for comparison
            min_length = min(data_orig.shape[1], data_prep.shape[1])
            data_orig = data_orig[:, :min_length]
            data_prep = data_prep[:, :min_length]
            
            snr_improvement = np.mean(np.std(data_orig, axis=1)) / np.mean(np.std(data_prep, axis=1))
            
            # Power spectral density
            freqs_orig, psd_orig = signal.welch(data_orig.flatten(), fs=self.sfreq, nperseg=256)
            freqs_prep, psd_prep = signal.welch(data_prep.flatten(), fs=self.sfreq, nperseg=256)
            
            line_freq_idx = np.argmin(np.abs(freqs_orig - 50))
            line_noise_reduction = psd_orig[line_freq_idx] / (psd_prep[line_freq_idx] + 1e-12)
            
            quality_metrics = {
                'snr_improvement': snr_improvement,
                'line_noise_reduction': line_noise_reduction,
                'preprocessing_success': True
            }
            
            logger.info(f"Preprocessing completed: SNR improvement: {snr_improvement:.2f}x")
            
            return {
                'raw_original': raw_original,
                'raw_preprocessed': raw_preprocessed,
                'quality_metrics': quality_metrics,
                'success': True,
                'file_path': file_path
            }
            
        except Exception as e:
            logger.error(f"Error processing {file_path}: {e}")
            return {'success': False, 'error': str(e), 'file_path': file_path}
    
    def plot_comparison(self, results, save_path=None):
        """
        Plot comparison between original and preprocessed data
        """
        if not results['success']:
            return
        
        raw_orig = results['raw_original']
        raw_prep = results['raw_preprocessed']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Select first channel for visualization
        ch_idx = 0
        ch_name = raw_orig.ch_names[ch_idx]
        
        # Time domain (first 10 seconds)
        max_samples = min(10 * int(self.sfreq), raw_orig.n_times, raw_prep.n_times)
        time = np.arange(max_samples) / self.sfreq
        
        axes[0, 0].plot(time, raw_orig.get_data()[ch_idx, :max_samples] * 1e6, 'b-', linewidth=0.5)
        axes[0, 0].set_title(f'Original Signal - {ch_name}', fontweight='bold')
        axes[0, 0].set_ylabel('Amplitude (μV)')
        axes[0, 0].grid(True, alpha=0.3)
        
        axes[0, 1].plot(time, raw_prep.get_data()[ch_idx, :max_samples] * 1e6, 'r-', linewidth=0.5)
        axes[0, 1].set_title(f'Preprocessed Signal - {ch_name}', fontweight='bold')
        axes[0, 1].set_ylabel('Amplitude (μV)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Power spectral density
        freqs_orig, psd_orig = signal.welch(raw_orig.get_data()[ch_idx, :], fs=self.sfreq, nperseg=256)
        freqs_prep, psd_prep = signal.welch(raw_prep.get_data()[ch_idx, :], fs=self.sfreq, nperseg=256)
        
        axes[1, 0].semilogy(freqs_orig, psd_orig * 1e12, 'b-', linewidth=1)
        axes[1, 0].set_title('Original PSD', fontweight='bold')
        axes[1, 0].set_ylabel('Power (μV²/Hz)')
        axes[1, 0].set_xlabel('Frequency (Hz)')
        axes[1, 0].set_xlim(0, 60)
        axes[1, 0].grid(True, alpha=0.3)
        
        axes[1, 1].semilogy(freqs_prep, psd_prep * 1e12, 'r-', linewidth=1)
        axes[1, 1].set_title('Preprocessed PSD', fontweight='bold')
        axes[1, 1].set_ylabel('Power (μV²/Hz)')
        axes[1, 1].set_xlabel('Frequency (Hz)')
        axes[1, 1].set_xlim(0, 60)
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Comparison plot saved: {save_path}")
        
        plt.show()


def main():
    """
    Main function for simplified preprocessing
    """
    print("Simplified Clinical EEG Preprocessing Pipeline")
    print("=" * 50)
    
    preprocessor = SimplifiedEEGPreprocessor()
    results = {}
    
    # Process Nigeria samples
    nigeria_files = [
        ('1252141/EEGs_Nigeria/signal-500-1.csv.gz', 'nigeria_epilepsy'),
        ('1252141/EEGs_Nigeria/signal-501-1.csv.gz', 'nigeria_epilepsy'),
        ('1252141/EEGs_Nigeria/signal-6-1.csv.gz', 'nigeria_control'),
        ('1252141/EEGs_Nigeria/signal-9-1.csv.gz', 'nigeria_control')
    ]
    
    for file_path, group in nigeria_files:
        if os.path.exists(file_path):
            result = preprocessor.load_and_preprocess(file_path, 'nigeria')
            if result['success']:
                results[f"{group}_{os.path.basename(file_path)}"] = result
    
    # Process Guinea-Bissau samples
    metadata_path = "1252141/metadata_guineabissau.csv"
    if os.path.exists(metadata_path):
        metadata = pd.read_csv(metadata_path)
        
        # Sample epilepsy and control subjects
        epilepsy_subject = metadata[metadata['Group'] == 'Epilepsy']['subject.id'].iloc[0]
        control_subject = metadata[metadata['Group'] == 'Control']['subject.id'].iloc[0]
        
        gb_files = [
            (f'1252141/EEGs_Guinea-Bissau/signal-{epilepsy_subject}.csv.gz', 'guinea_bissau_epilepsy'),
            (f'1252141/EEGs_Guinea-Bissau/signal-{control_subject}.csv.gz', 'guinea_bissau_control')
        ]
        
        for file_path, group in gb_files:
            if os.path.exists(file_path):
                result = preprocessor.load_and_preprocess(file_path, 'guinea_bissau')
                if result['success']:
                    results[f"{group}_{os.path.basename(file_path)}"] = result
    
    # Create comparison plots
    plot_count = 0
    for key, result in results.items():
        if result['success'] and plot_count < 4:  # Limit to 4 plots
            save_path = f"Simplified_Preprocessing_{key}.png"
            preprocessor.plot_comparison(result, save_path)
            plot_count += 1
    
    # Summary
    successful = sum(1 for r in results.values() if r['success'])
    print(f"\nPreprocessing Summary:")
    print(f"Successfully processed: {successful}/{len(results)} files")
    
    for key, result in results.items():
        if result['success']:
            metrics = result['quality_metrics']
            print(f"{key}: SNR improvement: {metrics['snr_improvement']:.2f}x, "
                  f"Line noise reduction: {metrics['line_noise_reduction']:.2f}x")
    
    return results


if __name__ == "__main__":
    main()
