#!/usr/bin/env python3
"""
直接信号可视化对比
Direct Signal Visualization Comparison

解决重建可视化空白问题:
1. 直接使用真实EEG信号段
2. 对比原始癫痫信号和伪装后的特征效果
3. 展示GAN如何改变信号的分类结果
4. 可视化特征空间的变化
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import tensorflow as tf
from tensorflow import keras
import warnings
import os
import gzip
import joblib
import pywt
from scipy import signal as scipy_signal
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

warnings.filterwarnings('ignore')

# 启用不安全反序列化
keras.config.enable_unsafe_deserialization()

# 定义自定义注意力层
class AttentionFeatureFusion(keras.layers.Layer):
    def __init__(self, **kwargs):
        super(AttentionFeatureFusion, self).__init__(**kwargs)
        
    def build(self, input_shape):
        self.W = self.add_weight(
            name='attention_weights',
            shape=(input_shape[-1], input_shape[-1]),
            initializer='glorot_uniform',
            trainable=True
        )
        self.b = self.add_weight(
            name='attention_bias',
            shape=(input_shape[-1],),
            initializer='zeros',
            trainable=True
        )
        super(AttentionFeatureFusion, self).build(input_shape)
    
    def call(self, inputs):
        attention_scores = tf.nn.tanh(tf.matmul(inputs, self.W) + self.b)
        attention_weights = tf.nn.softmax(attention_scores, axis=-1)
        weighted_features = inputs * attention_weights
        return weighted_features
    
    def get_config(self):
        config = super(AttentionFeatureFusion, self).get_config()
        return config


class DirectSignalVisualizer:
    """
    直接信号可视化器
    """
    
    def __init__(self):
        self.generator = None
        self.classifier = None
        self.signal_scaler = None
        self.feature_scaler = None
        
        print("直接信号可视化器初始化")
    
    def load_models(self):
        """
        加载模型
        """
        print("加载模型...")
        
        # 加载GAN生成器
        self.generator = keras.models.load_model('confidence_gan_results/confidence_based_generator.keras')
        print("✅ GAN生成器加载完成")
        
        # 加载分类器
        custom_objects = {'AttentionFeatureFusion': AttentionFeatureFusion}
        self.classifier = keras.models.load_model(
            'wavelet_classifier_results/wavelet_attention_classifier.keras',
            custom_objects=custom_objects
        )
        print("✅ 分类器加载完成")
        
        # 加载标准化器
        self.signal_scaler = joblib.load('wavelet_classifier_results/signal_scaler.pkl')
        self.feature_scaler = joblib.load('wavelet_classifier_results/feature_scaler.pkl')
        print("✅ 标准化器加载完成")
        
        return True
    
    def load_real_eeg_signals(self, num_samples=5):
        """
        加载真实的EEG信号段
        """
        print(f"加载真实EEG信号段 ({num_samples}个样本)...")
        
        # 加载Nigeria数据集元数据
        nigeria_metadata_path = "1252141/metadata_nigeria.csv"
        metadata = pd.read_csv(nigeria_metadata_path)
        
        # 获取癫痫组文件
        epilepsy_files = metadata[metadata['Group'] == 'epilepsy']['csv.file'].tolist()
        control_files = metadata[metadata['Group'] == 'control']['csv.file'].tolist()
        
        epilepsy_signals = []
        control_signals = []
        epilepsy_features = []
        control_features = []
        
        # 加载癫痫信号
        for filename in epilepsy_files[:10]:
            file_path = os.path.join("1252141/EEGs_Nigeria", filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                    
                    if 'FC5' in df.columns:
                        channel_data = df['FC5'].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values
                        
                        # 预处理
                        channel_data = self.preprocess_signal(channel_data)
                        
                        # 创建512点的信号段
                        segment_length = 512
                        step_size = segment_length // 2
                        
                        for i in range(0, len(channel_data) - segment_length + 1, step_size):
                            segment = channel_data[i:i + segment_length]
                            
                            if np.std(segment) > 0.01:
                                # 标准化
                                segment_scaled = self.signal_scaler.transform(segment.reshape(-1, 1)).flatten()
                                
                                # 提取特征
                                features = self.extract_features_from_signal(segment_scaled)
                                
                                epilepsy_signals.append(segment_scaled)
                                epilepsy_features.append(features)
                                
                                if len(epilepsy_signals) >= num_samples:
                                    break
                        
                        if len(epilepsy_signals) >= num_samples:
                            break
                
                except Exception as e:
                    print(f"跳过文件 {filename}: {e}")
        
        # 加载控制组信号
        for filename in control_files[:5]:
            file_path = os.path.join("1252141/EEGs_Nigeria", filename)
            
            if os.path.exists(file_path):
                try:
                    with gzip.open(file_path, 'rt') as f:
                        df = pd.read_csv(f)
                    
                    if 'FC5' in df.columns:
                        channel_data = df['FC5'].apply(pd.to_numeric, errors='coerce').fillna(method='ffill').values
                        
                        # 预处理
                        channel_data = self.preprocess_signal(channel_data)
                        
                        # 创建512点的信号段
                        segment_length = 512
                        step_size = segment_length // 2
                        
                        for i in range(0, len(channel_data) - segment_length + 1, step_size):
                            segment = channel_data[i:i + segment_length]
                            
                            if np.std(segment) > 0.01:
                                # 标准化
                                segment_scaled = self.signal_scaler.transform(segment.reshape(-1, 1)).flatten()
                                
                                # 提取特征
                                features = self.extract_features_from_signal(segment_scaled)
                                
                                control_signals.append(segment_scaled)
                                control_features.append(features)
                                
                                if len(control_signals) >= num_samples:
                                    break
                        
                        if len(control_signals) >= num_samples:
                            break
                
                except Exception as e:
                    print(f"跳过文件 {filename}: {e}")
        
        epilepsy_signals = np.array(epilepsy_signals[:num_samples])
        epilepsy_features = np.array(epilepsy_features[:num_samples])
        control_signals = np.array(control_signals[:num_samples])
        control_features = np.array(control_features[:num_samples])
        
        print(f"真实EEG信号加载完成:")
        print(f"- 癫痫信号: {epilepsy_signals.shape}")
        print(f"- 控制组信号: {control_signals.shape}")
        
        return epilepsy_signals, epilepsy_features, control_signals, control_features
    
    def preprocess_signal(self, signal):
        """
        预处理信号
        """
        # 去除异常值
        mean_val = np.mean(signal)
        std_val = np.std(signal)
        signal_clean = np.clip(signal, mean_val - 3*std_val, mean_val + 3*std_val)
        
        # 去趋势
        x = np.arange(len(signal_clean))
        coeffs = np.polyfit(x, signal_clean, 1)
        trend = np.polyval(coeffs, x)
        signal_detrend = signal_clean - trend
        
        # 带通滤波 (0.5-60Hz)
        sampling_rate = 128
        nyquist = sampling_rate / 2
        low_freq = 0.5 / nyquist
        high_freq = 60 / nyquist
        
        if low_freq < 1 and high_freq < 1:
            b, a = scipy_signal.butter(4, [low_freq, high_freq], btype='band')
            signal_filtered = scipy_signal.filtfilt(b, a, signal_detrend)
        else:
            signal_filtered = signal_detrend
        
        # 标准化
        signal_normalized = (signal_filtered - np.mean(signal_filtered)) / (np.std(signal_filtered) + 1e-8)
        
        return signal_normalized
    
    def extract_features_from_signal(self, signal):
        """
        从信号提取特征
        """
        # 小波特征
        wavelet_features = self.extract_wavelet_features(signal)
        
        # 时域特征
        time_features = self.extract_time_domain_features(signal)
        
        # 频域特征
        freq_features = self.extract_frequency_domain_features(signal)
        
        # 拼接特征
        all_features = np.hstack([wavelet_features, time_features, freq_features])
        
        # 标准化
        features_scaled = self.feature_scaler.transform(all_features.reshape(1, -1)).flatten()
        
        return features_scaled
    
    def extract_wavelet_features(self, signal):
        """
        提取小波特征
        """
        # 小波分解
        coeffs = pywt.wavedec(signal, 'db4', level=5)
        
        features = []
        
        # 近似系数
        cA = coeffs[0]
        features.extend([
            np.mean(cA), np.std(cA), np.var(cA),
            np.max(cA), np.min(cA),
            np.percentile(cA, 25), np.percentile(cA, 75),
            np.sum(cA ** 2),
            -np.sum(np.abs(cA) * np.log2(np.abs(cA) + 1e-12))
        ])
        
        # 细节系数
        for cD in coeffs[1:]:
            features.extend([
                np.mean(cD), np.std(cD), np.var(cD),
                np.max(cD), np.min(cD),
                np.percentile(cD, 25), np.percentile(cD, 75),
                np.sum(cD ** 2),
                -np.sum(np.abs(cD) * np.log2(np.abs(cD) + 1e-12)),
                np.sum(np.diff(np.sign(cD)) != 0) / len(cD)
            ])
        
        # 能量比
        energies = [np.sum(c ** 2) for c in coeffs]
        total_energy = sum(energies)
        energy_ratios = [e/total_energy for e in energies]
        features.extend(energy_ratios)
        
        # 相对小波能量
        features.extend([
            energies[0] / energies[1] if energies[1] > 0 else 0,
            max(energies[1:]) / energies[0] if energies[0] > 0 else 0
        ])
        
        return np.array(features)
    
    def extract_time_domain_features(self, signal):
        """
        提取时域特征
        """
        from scipy import stats
        
        features = [
            np.mean(signal), np.std(signal), np.var(signal),
            np.max(signal), np.min(signal), np.ptp(signal),
            np.percentile(signal, 25), np.percentile(signal, 75),
            np.median(signal), np.mean(np.abs(signal)),
            np.sqrt(np.mean(signal**2)),
            len(signal[signal > 0]) / len(signal),
            np.sum(np.diff(signal) > 0) / len(signal),
            stats.kurtosis(signal), stats.skew(signal)
        ]
        
        return np.array(features)
    
    def extract_frequency_domain_features(self, signal):
        """
        提取频域特征
        """
        sampling_rate = 128
        
        # FFT
        fft = np.fft.fft(signal)
        freqs = np.fft.fftfreq(len(signal), 1/sampling_rate)
        psd = np.abs(fft) ** 2
        
        # 频带划分
        bands = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]
        band_powers = []
        
        for low, high in bands:
            band_mask = (freqs >= low) & (freqs <= high)
            band_power = np.sum(psd[band_mask])
            band_powers.append(band_power)
        
        total_power = np.sum(band_powers)
        
        features = [
            np.mean(psd), np.std(psd), np.max(psd),
            freqs[np.argmax(psd)], total_power
        ]
        
        # 相对频带功率
        if total_power > 0:
            features.extend([p/total_power for p in band_powers])
        else:
            features.extend([0] * len(band_powers))
        
        # 频带功率比
        if band_powers[1] > 0:
            features.append(band_powers[0] / band_powers[1])
        else:
            features.append(0)
        
        if band_powers[0] > 0:
            features.append(band_powers[2] / band_powers[0])
        else:
            features.append(0)
        
        return np.array(features)

    def create_direct_comparison_visualization(self, epilepsy_signals, epilepsy_features,
                                             control_signals, control_features):
        """
        创建直接对比可视化
        """
        print("创建直接对比可视化...")

        # 1. 生成伪装特征
        fake_healthy_features = self.generator.predict(epilepsy_features, verbose=0)

        # 2. 分类器预测
        epilepsy_pred = self.classifier.predict(epilepsy_features, verbose=0)
        fake_pred = self.classifier.predict(fake_healthy_features, verbose=0)
        control_pred = self.classifier.predict(control_features, verbose=0)

        # 3. 创建主要可视化
        n_samples = len(epilepsy_signals)
        fig, axes = plt.subplots(n_samples, 4, figsize=(20, 5*n_samples))

        if n_samples == 1:
            axes = axes.reshape(1, -1)

        time_axis = np.arange(512) / 128  # 4秒

        for i in range(n_samples):
            # 原始癫痫信号
            axes[i, 0].plot(time_axis, epilepsy_signals[i], 'r-', linewidth=1.5, alpha=0.8)
            axes[i, 0].set_title(f'样本 {i+1}: 原始癫痫EEG信号\n'
                                f'分类器预测: {epilepsy_pred[i][0]:.3f} (癫痫概率)',
                                fontsize=12, fontweight='bold', color='red')
            axes[i, 0].set_xlabel('时间 (秒)')
            axes[i, 0].set_ylabel('标准化幅值')
            axes[i, 0].grid(True, alpha=0.3)
            axes[i, 0].set_ylim(-4, 4)

            # 对应的控制组信号
            axes[i, 1].plot(time_axis, control_signals[i], 'g-', linewidth=1.5, alpha=0.8)
            axes[i, 1].set_title(f'样本 {i+1}: 真实控制组EEG信号\n'
                                f'分类器预测: {control_pred[i][0]:.3f} (癫痫概率)',
                                fontsize=12, fontweight='bold', color='green')
            axes[i, 1].set_xlabel('时间 (秒)')
            axes[i, 1].set_ylabel('标准化幅值')
            axes[i, 1].grid(True, alpha=0.3)
            axes[i, 1].set_ylim(-4, 4)

            # 信号叠加对比
            axes[i, 2].plot(time_axis, epilepsy_signals[i], 'r-', linewidth=2,
                           alpha=0.7, label=f'癫痫信号 (预测:{epilepsy_pred[i][0]:.3f})')
            axes[i, 2].plot(time_axis, control_signals[i], 'g-', linewidth=2,
                           alpha=0.7, label=f'控制组信号 (预测:{control_pred[i][0]:.3f})')
            axes[i, 2].set_title(f'样本 {i+1}: 癫痫 vs 控制组信号对比\n'
                                f'GAN伪装效果: {fake_pred[i][0]:.3f} → {1-fake_pred[i][0]:.3f}',
                                fontsize=12, fontweight='bold')
            axes[i, 2].set_xlabel('时间 (秒)')
            axes[i, 2].set_ylabel('标准化幅值')
            axes[i, 2].legend()
            axes[i, 2].grid(True, alpha=0.3)
            axes[i, 2].set_ylim(-4, 4)

            # 特征空间对比
            feature_names = ['原始癫痫特征', '伪装健康特征', '真实控制组特征']
            feature_preds = [epilepsy_pred[i][0], fake_pred[i][0], control_pred[i][0]]
            colors = ['red', 'orange', 'green']

            bars = axes[i, 3].bar(feature_names, feature_preds, color=colors, alpha=0.7)
            axes[i, 3].set_title(f'样本 {i+1}: 分类器预测对比\n'
                                f'伪装成功率: {(1-fake_pred[i][0])*100:.1f}%',
                                fontsize=12, fontweight='bold')
            axes[i, 3].set_ylabel('癫痫预测概率')
            axes[i, 3].set_ylim(0, 1)
            axes[i, 3].tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, pred in zip(bars, feature_preds):
                axes[i, 3].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                               f'{pred:.3f}', ha='center', va='bottom', fontweight='bold')

            axes[i, 3].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('真实EEG信号与GAN伪装效果对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 4. 创建频谱对比
        self.create_frequency_spectrum_comparison(epilepsy_signals, control_signals,
                                                epilepsy_pred, fake_pred, control_pred)

        # 5. 创建特征空间可视化
        self.create_feature_space_visualization(epilepsy_features, fake_healthy_features,
                                              control_features, epilepsy_pred, fake_pred, control_pred)

        print("直接对比可视化完成!")

        return {
            'epilepsy_signals': epilepsy_signals,
            'control_signals': control_signals,
            'epilepsy_features': epilepsy_features,
            'fake_features': fake_healthy_features,
            'control_features': control_features,
            'epilepsy_pred': epilepsy_pred,
            'fake_pred': fake_pred,
            'control_pred': control_pred
        }

    def create_frequency_spectrum_comparison(self, epilepsy_signals, control_signals,
                                           epilepsy_pred, fake_pred, control_pred):
        """
        创建频谱对比
        """
        print("创建频谱对比...")

        n_samples = len(epilepsy_signals)
        fig, axes = plt.subplots(n_samples, 2, figsize=(15, 4*n_samples))

        if n_samples == 1:
            axes = axes.reshape(1, -1)

        sampling_rate = 128
        freqs = np.fft.fftfreq(512, 1/sampling_rate)[:256]

        for i in range(n_samples):
            # 计算功率谱密度
            epilepsy_fft = np.fft.fft(epilepsy_signals[i])
            epilepsy_psd = np.abs(epilepsy_fft[:256]) ** 2

            control_fft = np.fft.fft(control_signals[i])
            control_psd = np.abs(control_fft[:256]) ** 2

            # 功率谱对比
            axes[i, 0].semilogy(freqs, epilepsy_psd, 'r-', linewidth=2,
                               alpha=0.8, label=f'癫痫信号 (预测:{epilepsy_pred[i][0]:.3f})')
            axes[i, 0].semilogy(freqs, control_psd, 'g-', linewidth=2,
                               alpha=0.8, label=f'控制组信号 (预测:{control_pred[i][0]:.3f})')
            axes[i, 0].set_title(f'样本 {i+1}: 功率谱密度对比', fontsize=12, fontweight='bold')
            axes[i, 0].set_xlabel('频率 (Hz)')
            axes[i, 0].set_ylabel('功率谱密度')
            axes[i, 0].legend()
            axes[i, 0].grid(True, alpha=0.3)
            axes[i, 0].set_xlim(0, 60)

            # 频带能量对比
            bands = ['Delta\n(0.5-4Hz)', 'Theta\n(4-8Hz)', 'Alpha\n(8-13Hz)',
                    'Beta\n(13-30Hz)', 'Gamma\n(30-60Hz)']
            band_ranges = [(0.5, 4), (4, 8), (8, 13), (13, 30), (30, 60)]

            epilepsy_band_powers = []
            control_band_powers = []

            for low, high in band_ranges:
                band_mask = (freqs >= low) & (freqs <= high)
                epilepsy_band_powers.append(np.sum(epilepsy_psd[band_mask]))
                control_band_powers.append(np.sum(control_psd[band_mask]))

            x = np.arange(len(bands))
            width = 0.35

            axes[i, 1].bar(x - width/2, epilepsy_band_powers, width,
                          label=f'癫痫信号', color='red', alpha=0.7)
            axes[i, 1].bar(x + width/2, control_band_powers, width,
                          label=f'控制组信号', color='green', alpha=0.7)

            axes[i, 1].set_title(f'样本 {i+1}: 频带能量对比\n'
                                f'GAN伪装: {epilepsy_pred[i][0]:.3f} → {fake_pred[i][0]:.3f}',
                                fontsize=12, fontweight='bold')
            axes[i, 1].set_xlabel('频带')
            axes[i, 1].set_ylabel('能量')
            axes[i, 1].set_xticks(x)
            axes[i, 1].set_xticklabels(bands)
            axes[i, 1].legend()
            axes[i, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('真实EEG信号频谱对比.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("频谱对比完成!")

    def create_feature_space_visualization(self, epilepsy_features, fake_features,
                                         control_features, epilepsy_pred, fake_pred, control_pred):
        """
        创建特征空间可视化
        """
        print("创建特征空间可视化...")

        from sklearn.decomposition import PCA
        from sklearn.manifold import TSNE

        # 合并所有特征
        all_features = np.vstack([epilepsy_features, fake_features, control_features])
        n_epilepsy = len(epilepsy_features)
        n_fake = len(fake_features)

        # PCA降维
        pca = PCA(n_components=2)
        features_pca = pca.fit_transform(all_features)

        epilepsy_pca = features_pca[:n_epilepsy]
        fake_pca = features_pca[n_epilepsy:n_epilepsy+n_fake]
        control_pca = features_pca[n_epilepsy+n_fake:]

        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42, perplexity=min(5, len(all_features)-1))
        features_tsne = tsne.fit_transform(all_features)

        epilepsy_tsne = features_tsne[:n_epilepsy]
        fake_tsne = features_tsne[n_epilepsy:n_epilepsy+n_fake]
        control_tsne = features_tsne[n_epilepsy+n_fake:]

        # 创建可视化
        fig, axes = plt.subplots(1, 2, figsize=(16, 6))

        # PCA可视化
        axes[0].scatter(epilepsy_pca[:, 0], epilepsy_pca[:, 1], c='red',
                       label=f'原始癫痫特征 (平均预测:{np.mean(epilepsy_pred):.3f})',
                       alpha=0.7, s=100, marker='o')
        axes[0].scatter(fake_pca[:, 0], fake_pca[:, 1], c='orange',
                       label=f'伪装健康特征 (平均预测:{np.mean(fake_pred):.3f})',
                       alpha=0.7, s=100, marker='^')
        axes[0].scatter(control_pca[:, 0], control_pca[:, 1], c='green',
                       label=f'真实控制组特征 (平均预测:{np.mean(control_pred):.3f})',
                       alpha=0.7, s=100, marker='s')

        axes[0].set_title('特征空间分布 (PCA)', fontsize=14, fontweight='bold')
        axes[0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.1%} 方差)')
        axes[0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.1%} 方差)')
        axes[0].legend()
        axes[0].grid(True, alpha=0.3)

        # t-SNE可视化
        axes[1].scatter(epilepsy_tsne[:, 0], epilepsy_tsne[:, 1], c='red',
                       label=f'原始癫痫特征', alpha=0.7, s=100, marker='o')
        axes[1].scatter(fake_tsne[:, 0], fake_tsne[:, 1], c='orange',
                       label=f'伪装健康特征', alpha=0.7, s=100, marker='^')
        axes[1].scatter(control_tsne[:, 0], control_tsne[:, 1], c='green',
                       label=f'真实控制组特征', alpha=0.7, s=100, marker='s')

        axes[1].set_title('特征空间分布 (t-SNE)', fontsize=14, fontweight='bold')
        axes[1].set_xlabel('t-SNE 1')
        axes[1].set_ylabel('t-SNE 2')
        axes[1].legend()
        axes[1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('特征空间分布可视化.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("特征空间可视化完成!")


def main():
    """
    主函数
    """
    print("=" * 80)
    print("直接信号可视化对比")
    print("=" * 80)
    
    # 初始化可视化器
    visualizer = DirectSignalVisualizer()
    
    # 加载模型
    if not visualizer.load_models():
        print("❌ 模型加载失败")
        return
    
    # 加载真实EEG信号
    epilepsy_signals, epilepsy_features, control_signals, control_features = \
        visualizer.load_real_eeg_signals(num_samples=3)

    # 创建直接对比可视化
    print("\n创建直接对比可视化...")
    results = visualizer.create_direct_comparison_visualization(
        epilepsy_signals, epilepsy_features, control_signals, control_features
    )

    print("\n" + "=" * 80)
    print("直接信号可视化对比完成!")
    print("=" * 80)
    print("主要发现:")
    print(f"- 原始癫痫信号平均预测概率: {np.mean(results['epilepsy_pred']):.3f}")
    print(f"- 伪装后平均预测概率: {np.mean(results['fake_pred']):.3f}")
    print(f"- 真实控制组平均预测概率: {np.mean(results['control_pred']):.3f}")
    print(f"- 平均伪装成功率: {np.mean(1 - results['fake_pred'])*100:.1f}%")

    # 计算伪装效果
    masking_effectiveness = []
    for i in range(len(results['epilepsy_pred'])):
        original_prob = results['epilepsy_pred'][i][0]
        fake_prob = results['fake_pred'][i][0]
        effectiveness = (original_prob - fake_prob) / original_prob if original_prob > 0 else 0
        masking_effectiveness.append(effectiveness)

    print(f"- 平均病灶掩盖效果: {np.mean(masking_effectiveness):.3f}")

    print("\n🎯 关键成果:")
    print(f"✅ 成功展示了 {len(epilepsy_signals)} 个真实EEG信号的GAN伪装效果")
    print(f"✅ 癫痫信号被成功伪装为健康信号 (平均成功率: {np.mean(1 - results['fake_pred'])*100:.1f}%)")
    print(f"✅ 特征空间中伪装特征向控制组特征靠近")

    print("\n生成的可视化文件:")
    print("- 真实EEG信号与GAN伪装效果对比.png")
    print("- 真实EEG信号频谱对比.png")
    print("- 特征空间分布可视化.png")

    return visualizer, results


if __name__ == "__main__":
    visualizer, results = main()
