#!/usr/bin/env python3
"""
Epilepsy Autoencoder Pipeline
将提取的癫痫分解波拆分成多段，预处理，并构建自动编码器模型进行重建训练

This pipeline implements:
1. 分解波分段 (Wave Segmentation)
2. 数据预处理 (Data Preprocessing) 
3. 自动编码器构建 (Autoencoder Construction)
4. 模型训练 (Model Training)
5. 重建评估 (Reconstruction Evaluation)
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
import tensorflow as tf
from tensorflow import keras
from tensorflow.keras import layers
import warnings
import os
from pathlib import Path

warnings.filterwarnings('ignore')

class EpilepsyAutoencoderPipeline:
    """
    癫痫分解波自动编码器训练管道
    """
    
    def __init__(self, segment_length=256, overlap_ratio=0.5, sampling_rate=128):
        """
        初始化管道参数
        
        Parameters:
        -----------
        segment_length : int
            每段的长度 (样本点数)
        overlap_ratio : float
            重叠比例 (0-1)
        sampling_rate : int
            采样率 (Hz)
        """
        self.segment_length = segment_length
        self.overlap_ratio = overlap_ratio
        self.sampling_rate = sampling_rate
        self.step_size = int(segment_length * (1 - overlap_ratio))
        
        # 存储处理后的数据
        self.segments = None
        self.scaler = None
        self.autoencoder = None
        self.encoder = None
        self.decoder = None
        self.history = None
        
        print(f"初始化自动编码器管道:")
        print(f"- 分段长度: {segment_length} 样本点 ({segment_length/sampling_rate:.2f} 秒)")
        print(f"- 重叠比例: {overlap_ratio*100:.1f}%")
        print(f"- 步长: {self.step_size} 样本点")
    
    def load_gamma_waves(self, dataset_path):
        """
        加载提取的Gamma波数据
        
        Parameters:
        -----------
        dataset_path : str
            数据集路径
            
        Returns:
        --------
        gamma_waves : numpy.ndarray
            Gamma波数据
        metadata : pandas.DataFrame
            元数据
        """
        print("加载Gamma波数据...")
        
        # 加载Gamma波
        gamma_waves = np.load(os.path.join(dataset_path, 'epilepsy_gamma_waves.npy'))
        
        # 加载元数据
        metadata = pd.read_csv(os.path.join(dataset_path, 'epilepsy_metadata.csv'))
        
        print(f"加载完成: {gamma_waves.shape[0]} 个样本, 每个样本 {gamma_waves.shape[1]} 个时间点")
        
        return gamma_waves, metadata
    
    def segment_waves(self, gamma_waves):
        """
        将Gamma波分割成多个重叠的段
        
        Parameters:
        -----------
        gamma_waves : numpy.ndarray
            原始Gamma波数据 (n_samples, n_timepoints)
            
        Returns:
        --------
        segments : numpy.ndarray
            分割后的段 (n_segments, segment_length)
        segment_info : list
            每个段的信息 (sample_idx, start_idx, end_idx)
        """
        print("开始分割Gamma波...")
        
        all_segments = []
        segment_info = []
        
        for sample_idx, wave in enumerate(gamma_waves):
            # 计算该样本可以分割的段数
            n_segments = (len(wave) - self.segment_length) // self.step_size + 1
            
            for seg_idx in range(n_segments):
                start_idx = seg_idx * self.step_size
                end_idx = start_idx + self.segment_length
                
                if end_idx <= len(wave):
                    segment = wave[start_idx:end_idx]
                    all_segments.append(segment)
                    segment_info.append({
                        'sample_idx': sample_idx,
                        'segment_idx': seg_idx,
                        'start_idx': start_idx,
                        'end_idx': end_idx
                    })
        
        segments = np.array(all_segments)
        
        print(f"分割完成: 总共 {len(segments)} 个段")
        print(f"每个段长度: {self.segment_length} 样本点")
        
        self.segments = segments
        self.segment_info = segment_info
        
        return segments, segment_info
    
    def preprocess_segments(self, segments, method='standardize'):
        """
        预处理分割的段
        
        Parameters:
        -----------
        segments : numpy.ndarray
            分割的段
        method : str
            预处理方法 ('standardize', 'normalize', 'minmax')
            
        Returns:
        --------
        processed_segments : numpy.ndarray
            预处理后的段
        """
        print(f"开始预处理段数据 (方法: {method})...")
        
        # 重塑数据用于缩放
        n_segments, segment_length = segments.shape
        segments_reshaped = segments.reshape(-1, 1)
        
        if method == 'standardize':
            self.scaler = StandardScaler()
        elif method == 'minmax':
            self.scaler = MinMaxScaler(feature_range=(-1, 1))
        elif method == 'normalize':
            self.scaler = MinMaxScaler(feature_range=(0, 1))
        else:
            raise ValueError(f"未知的预处理方法: {method}")
        
        # 拟合并转换数据
        segments_scaled = self.scaler.fit_transform(segments_reshaped)
        processed_segments = segments_scaled.reshape(n_segments, segment_length)
        
        print(f"预处理完成:")
        print(f"- 原始数据范围: [{segments.min():.4f}, {segments.max():.4f}]")
        print(f"- 处理后范围: [{processed_segments.min():.4f}, {processed_segments.max():.4f}]")
        
        return processed_segments
    
    def build_autoencoder(self, input_dim, encoding_dim=64, hidden_dims=[128, 64]):
        """
        构建自动编码器模型
        
        Parameters:
        -----------
        input_dim : int
            输入维度 (段长度)
        encoding_dim : int
            编码维度 (潜在空间维度)
        hidden_dims : list
            隐藏层维度列表
            
        Returns:
        --------
        autoencoder : keras.Model
            完整的自动编码器模型
        encoder : keras.Model
            编码器模型
        decoder : keras.Model
            解码器模型
        """
        print("构建自动编码器模型...")
        
        # 输入层
        input_layer = keras.Input(shape=(input_dim,))
        
        # 编码器部分
        encoded = input_layer
        for dim in hidden_dims:
            encoded = layers.Dense(dim, activation='relu')(encoded)
            encoded = layers.Dropout(0.2)(encoded)
        
        # 潜在空间 (编码层)
        encoded = layers.Dense(encoding_dim, activation='relu', name='encoding')(encoded)
        
        # 解码器部分
        decoded = encoded
        for dim in reversed(hidden_dims):
            decoded = layers.Dense(dim, activation='relu')(decoded)
            decoded = layers.Dropout(0.2)(decoded)
        
        # 输出层 (重建)
        decoded = layers.Dense(input_dim, activation='linear')(decoded)
        
        # 构建模型
        autoencoder = keras.Model(input_layer, decoded, name='autoencoder')
        encoder = keras.Model(input_layer, encoded, name='encoder')
        
        # 解码器模型 - 重新构建
        encoded_input = keras.Input(shape=(encoding_dim,))
        decoded_output = encoded_input

        # 重建解码器层
        for dim in reversed(hidden_dims):
            decoded_output = layers.Dense(dim, activation='relu')(decoded_output)
            decoded_output = layers.Dropout(0.2)(decoded_output)

        # 输出层
        decoded_output = layers.Dense(input_dim, activation='linear')(decoded_output)

        decoder = keras.Model(encoded_input, decoded_output, name='decoder')
        
        # 编译模型
        autoencoder.compile(
            optimizer='adam',
            loss='mse',
            metrics=['mae']
        )
        
        print("模型构建完成:")
        print(f"- 输入维度: {input_dim}")
        print(f"- 编码维度: {encoding_dim}")
        print(f"- 隐藏层: {hidden_dims}")
        
        self.autoencoder = autoencoder
        self.encoder = encoder
        self.decoder = decoder
        
        return autoencoder, encoder, decoder
    
    def train_autoencoder(self, X_train, X_val=None, epochs=100, batch_size=32, 
                         validation_split=0.2, early_stopping=True):
        """
        训练自动编码器
        
        Parameters:
        -----------
        X_train : numpy.ndarray
            训练数据
        X_val : numpy.ndarray, optional
            验证数据
        epochs : int
            训练轮数
        batch_size : int
            批次大小
        validation_split : float
            验证集比例 (如果X_val为None)
        early_stopping : bool
            是否使用早停
            
        Returns:
        --------
        history : keras.callbacks.History
            训练历史
        """
        print("开始训练自动编码器...")
        
        # 准备回调函数
        callbacks = []
        
        if early_stopping:
            early_stop = keras.callbacks.EarlyStopping(
                monitor='val_loss',
                patience=10,
                restore_best_weights=True,
                verbose=1
            )
            callbacks.append(early_stop)
        
        # 学习率调度
        lr_scheduler = keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=5,
            min_lr=1e-6,
            verbose=1
        )
        callbacks.append(lr_scheduler)
        
        # 训练模型
        if X_val is not None:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_data=(X_val, X_val),
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        else:
            history = self.autoencoder.fit(
                X_train, X_train,
                validation_split=validation_split,
                epochs=epochs,
                batch_size=batch_size,
                callbacks=callbacks,
                verbose=1
            )
        
        self.history = history
        
        print("训练完成!")
        return history
    
    def evaluate_reconstruction(self, X_test, n_samples=5):
        """
        评估重建效果
        
        Parameters:
        -----------
        X_test : numpy.ndarray
            测试数据
        n_samples : int
            显示的样本数量
            
        Returns:
        --------
        reconstruction_error : numpy.ndarray
            重建误差
        """
        print("评估重建效果...")
        
        # 预测重建
        X_reconstructed = self.autoencoder.predict(X_test, verbose=0)
        
        # 计算重建误差
        reconstruction_error = np.mean((X_test - X_reconstructed) ** 2, axis=1)
        
        print(f"重建误差统计:")
        print(f"- 平均误差: {np.mean(reconstruction_error):.6f}")
        print(f"- 标准差: {np.std(reconstruction_error):.6f}")
        print(f"- 最小误差: {np.min(reconstruction_error):.6f}")
        print(f"- 最大误差: {np.max(reconstruction_error):.6f}")
        
        # 可视化重建结果
        self.visualize_reconstruction(X_test, X_reconstructed, n_samples)
        
        return reconstruction_error
    
    def visualize_reconstruction(self, original, reconstructed, n_samples=5):
        """
        可视化重建结果
        
        Parameters:
        -----------
        original : numpy.ndarray
            原始数据
        reconstructed : numpy.ndarray
            重建数据
        n_samples : int
            显示的样本数量
        """
        fig, axes = plt.subplots(n_samples, 1, figsize=(15, 12))
        if n_samples == 1:
            axes = [axes]
        
        time = np.arange(self.segment_length) / self.sampling_rate
        
        for i in range(min(n_samples, len(original))):
            axes[i].plot(time, original[i], 'b-', label='原始信号', linewidth=1.5)
            axes[i].plot(time, reconstructed[i], 'r--', label='重建信号', linewidth=1.5)
            axes[i].set_title(f'重建对比 - 段 {i+1}')
            axes[i].set_xlabel('时间 (秒)')
            axes[i].set_ylabel('幅值')
            axes[i].legend()
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('autoencoder_reconstruction_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_training_history(self):
        """
        绘制训练历史
        """
        if self.history is None:
            print("没有训练历史可显示")
            return
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
        
        # 损失曲线
        ax1.plot(self.history.history['loss'], 'b-', label='训练损失')
        if 'val_loss' in self.history.history:
            ax1.plot(self.history.history['val_loss'], 'r-', label='验证损失')
        ax1.set_title('模型损失')
        ax1.set_xlabel('轮数')
        ax1.set_ylabel('损失')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # MAE曲线
        ax2.plot(self.history.history['mae'], 'b-', label='训练MAE')
        if 'val_mae' in self.history.history:
            ax2.plot(self.history.history['val_mae'], 'r-', label='验证MAE')
        ax2.set_title('平均绝对误差')
        ax2.set_xlabel('轮数')
        ax2.set_ylabel('MAE')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('autoencoder_training_history.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def save_models(self, save_dir='autoencoder_models'):
        """
        保存训练好的模型
        
        Parameters:
        -----------
        save_dir : str
            保存目录
        """
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存模型
        self.autoencoder.save(os.path.join(save_dir, 'autoencoder.h5'))
        self.encoder.save(os.path.join(save_dir, 'encoder.h5'))
        
        # 保存缩放器
        import joblib
        joblib.dump(self.scaler, os.path.join(save_dir, 'scaler.pkl'))
        
        print(f"模型已保存到 {save_dir}/")


def main():
    """
    主函数 - 执行完整的自动编码器训练管道
    """
    print("=" * 60)
    print("癫痫分解波自动编码器训练管道")
    print("=" * 60)
    
    # 初始化管道
    pipeline = EpilepsyAutoencoderPipeline(
        segment_length=256,  # 2秒的段 (256/128)
        overlap_ratio=0.5,   # 50%重叠
        sampling_rate=128
    )
    
    # 步骤1: 加载数据
    print("\n步骤1: 加载Gamma波数据")
    gamma_waves, metadata = pipeline.load_gamma_waves('epilepsy_wavelet_dataset')
    
    # 步骤2: 分割波形
    print("\n步骤2: 分割Gamma波")
    segments, segment_info = pipeline.segment_waves(gamma_waves)
    
    # 步骤3: 预处理
    print("\n步骤3: 预处理段数据")
    processed_segments = pipeline.preprocess_segments(segments, method='standardize')
    
    # 步骤4: 划分训练/测试集
    print("\n步骤4: 划分数据集")
    X_train, X_test = train_test_split(
        processed_segments, 
        test_size=0.2, 
        random_state=42
    )
    print(f"训练集: {X_train.shape[0]} 个段")
    print(f"测试集: {X_test.shape[0]} 个段")
    
    # 步骤5: 构建模型
    print("\n步骤5: 构建自动编码器")
    autoencoder, encoder, decoder = pipeline.build_autoencoder(
        input_dim=pipeline.segment_length,
        encoding_dim=32,
        hidden_dims=[128, 64]
    )
    
    # 显示模型结构
    print("\n自动编码器结构:")
    autoencoder.summary()
    
    # 步骤6: 训练模型
    print("\n步骤6: 训练自动编码器")
    history = pipeline.train_autoencoder(
        X_train,
        epochs=50,
        batch_size=32,
        validation_split=0.2,
        early_stopping=True
    )
    
    # 步骤7: 评估模型
    print("\n步骤7: 评估重建效果")
    reconstruction_error = pipeline.evaluate_reconstruction(X_test, n_samples=5)
    
    # 步骤8: 可视化结果
    print("\n步骤8: 可视化训练历史")
    pipeline.plot_training_history()
    
    # 步骤9: 保存模型
    print("\n步骤9: 保存模型")
    pipeline.save_models()
    
    print("\n=" * 60)
    print("自动编码器训练管道完成!")
    print("=" * 60)


if __name__ == "__main__":
    main()
